# 图表响应式缩放功能说明

## 功能概述

为 Chart1 和 Chart2 组件增强了响应式缩放功能，确保图表在屏幕放大、缩小或容器尺寸变化时能够自动调整大小。

## 增强功能

### 1. 多重监听机制

#### 原始功能
```javascript
// 只监听窗口大小变化
window.addEventListener('resize', handleResize)
```

#### 增强后功能
```javascript
// 1. 窗口大小变化
window.addEventListener('resize', handleResize)

// 2. 设备方向变化（移动端）
window.addEventListener('orientationchange', handleResize)

// 3. 容器尺寸变化（ResizeObserver）
resizeObserver = new ResizeObserver(() => {
  handleResize()
})
resizeObserver.observe(chartRef.value)
```

### 2. 防抖处理

#### 原始处理
```javascript
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}
```

#### 增强后处理
```javascript
const handleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }, 100) // 100ms 防抖
}
```

### 3. 完善的生命周期管理

```javascript
onMounted(async () => {
  await nextTick() // 确保 DOM 已渲染
  initChart()
  initResizeObserver()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  
  // 监听浏览器缩放
  window.addEventListener('orientationchange', handleResize)
  
  // 延迟执行一次 resize，确保图表正确渲染
  setTimeout(() => {
    handleResize()
  }, 300)
})

onUnmounted(() => {
  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  
  // 清理 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  // 移除事件监听器
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('orientationchange', handleResize)
})
```

## 技术原理

### 1. ResizeObserver API

```javascript
const initResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined' && chartRef.value) {
    resizeObserver = new ResizeObserver(() => {
      handleResize()
    })
    resizeObserver.observe(chartRef.value)
  }
}
```

**优势：**
- **精确监听**：只监听目标元素的尺寸变化
- **性能优化**：比 window.resize 更高效
- **容器感知**：即使窗口大小不变，容器变化也能检测到

### 2. 防抖机制

```javascript
let resizeTimer = null

const handleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }, 100)
}
```

**作用：**
- **避免频繁调用**：防止在快速调整窗口大小时频繁重绘
- **性能优化**：减少不必要的计算和渲染
- **用户体验**：避免图表闪烁

### 3. 多事件监听

```javascript
// 窗口大小变化
window.addEventListener('resize', handleResize)

// 设备方向变化（移动端横竖屏切换）
window.addEventListener('orientationchange', handleResize)
```

**覆盖场景：**
- **桌面端**：窗口拖拽、最大化/最小化
- **移动端**：横竖屏切换
- **浏览器**：缩放级别变化
- **容器**：父元素尺寸变化

## 响应场景

### 1. 浏览器窗口调整

**场景：**
- 拖拽窗口边缘调整大小
- 最大化/最小化窗口
- 分屏显示

**响应：**
- `window.resize` 事件触发
- 图表自动调整到新的容器尺寸

### 2. 浏览器缩放

**场景：**
- Ctrl + 滚轮缩放
- Ctrl + Plus/Minus 缩放
- 浏览器缩放设置

**响应：**
- `window.resize` 事件触发
- 图表重新计算尺寸和比例

### 3. 移动端方向变化

**场景：**
- 手机/平板横竖屏切换
- 设备旋转

**响应：**
- `orientationchange` 事件触发
- 图表适应新的屏幕方向

### 4. 容器尺寸变化

**场景：**
- 父容器动态调整大小
- CSS 动画改变容器尺寸
- JavaScript 动态修改样式

**响应：**
- `ResizeObserver` 监听触发
- 图表跟随容器尺寸变化

## 兼容性说明

### ResizeObserver 兼容性

| 浏览器 | 版本支持 |
|--------|----------|
| Chrome | 64+ |
| Firefox | 69+ |
| Safari | 13.1+ |
| Edge | 79+ |

### 降级方案

```javascript
const initResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined' && chartRef.value) {
    // 支持 ResizeObserver
    resizeObserver = new ResizeObserver(() => {
      handleResize()
    })
    resizeObserver.observe(chartRef.value)
  } else {
    // 降级方案：使用定时器轮询
    const pollResize = () => {
      const currentWidth = chartRef.value?.offsetWidth
      const currentHeight = chartRef.value?.offsetHeight
      
      if (currentWidth !== lastWidth || currentHeight !== lastHeight) {
        handleResize()
        lastWidth = currentWidth
        lastHeight = currentHeight
      }
    }
    
    setInterval(pollResize, 500) // 每500ms检查一次
  }
}
```

## 性能优化

### 1. 防抖延迟调优

```javascript
// 不同场景的防抖延迟
const RESIZE_DEBOUNCE_DELAY = {
  desktop: 100,   // 桌面端：100ms
  mobile: 200,    // 移动端：200ms
  animation: 50   // 动画场景：50ms
}
```

### 2. 条件渲染

```javascript
const handleResize = () => {
  // 只有在图表可见时才重绘
  if (chartInstance && chartRef.value?.offsetParent !== null) {
    chartInstance.resize()
  }
}
```

### 3. 批量更新

```javascript
let pendingResize = false

const handleResize = () => {
  if (pendingResize) return
  
  pendingResize = true
  requestAnimationFrame(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
    pendingResize = false
  })
}
```

## 测试验证

### 1. 手动测试

**桌面端测试：**
1. 拖拽浏览器窗口边缘
2. 使用 Ctrl + 滚轮缩放
3. 按 F11 全屏/退出全屏
4. 最大化/最小化窗口

**移动端测试：**
1. 旋转设备（横竖屏切换）
2. 浏览器地址栏显示/隐藏
3. 虚拟键盘弹出/收起

### 2. 自动化测试

```javascript
// 模拟窗口大小变化
const testResize = () => {
  const originalWidth = window.innerWidth
  const originalHeight = window.innerHeight
  
  // 模拟窗口变化
  Object.defineProperty(window, 'innerWidth', { value: 800 })
  Object.defineProperty(window, 'innerHeight', { value: 600 })
  
  // 触发 resize 事件
  window.dispatchEvent(new Event('resize'))
  
  // 验证图表是否重新渲染
  setTimeout(() => {
    const chartWidth = chartRef.value.offsetWidth
    console.log('Chart resized to:', chartWidth)
    
    // 恢复原始尺寸
    Object.defineProperty(window, 'innerWidth', { value: originalWidth })
    Object.defineProperty(window, 'innerHeight', { value: originalHeight })
  }, 200)
}
```

### 3. 性能监控

```javascript
const performanceMonitor = {
  resizeCount: 0,
  lastResizeTime: 0,
  
  trackResize() {
    this.resizeCount++
    this.lastResizeTime = Date.now()
    
    if (this.resizeCount % 10 === 0) {
      console.log(`Resize called ${this.resizeCount} times`)
    }
  }
}

const handleResize = () => {
  performanceMonitor.trackResize()
  
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }, 100)
}
```

## 常见问题解决

### Q: 图表在某些情况下不响应缩放？

**A:** 检查以下几点：
1. 确保图表容器有明确的宽高
2. 检查 CSS 是否有 `overflow: hidden`
3. 验证 ResizeObserver 是否正确绑定

### Q: 缩放时图表闪烁？

**A:** 调整防抖延迟：
```javascript
// 增加防抖延迟
resizeTimer = setTimeout(() => {
  if (chartInstance) {
    chartInstance.resize()
  }
}, 200) // 从100ms增加到200ms
```

### Q: 移动端横竖屏切换不响应？

**A:** 确保添加了 orientationchange 监听：
```javascript
window.addEventListener('orientationchange', handleResize)
```

## 总结

通过以上增强，图表组件现在具备了：

- ✅ **全面的响应式支持**：窗口、缩放、方向、容器变化
- ✅ **性能优化**：防抖处理、条件渲染
- ✅ **兼容性保障**：ResizeObserver 降级方案
- ✅ **完善的清理机制**：避免内存泄漏
- ✅ **用户体验优化**：平滑的缩放效果

现在当您放大或缩小屏幕时，Chart1 和 Chart2 都会自动跟随缩放，提供最佳的视觉体验！
