# CustomModal 自定义弹窗组件使用说明

## 概述

CustomModal 是一个功能强大的自定义弹窗组件，用于替代 Element Plus 的 el-dialog，提供了更好的动画效果和更灵活的配置选项。

## 特性

✨ **丰富的动画效果**：支持 8 种不同的打开动画
🎨 **灵活的尺寸配置**：支持小、中、大、全屏四种尺寸
🎯 **完善的交互体验**：支持 ESC 键关闭、点击遮罩关闭等
🔧 **高度可定制**：支持自定义标题、内容、底部按钮
📱 **响应式设计**：自适应不同屏幕尺寸

## 动画效果

| 动画类型 | 描述 | 效果 |
|---------|------|------|
| `fade-scale` | 淡入放大 | 从小到大淡入显示 |
| `slide-down` | 从上滑入 | 从顶部向下滑入 |
| `slide-up` | 从下滑入 | 从底部向上滑入 |
| `slide-left` | 从左滑入 | 从左侧向右滑入 |
| `slide-right` | 从右滑入 | 从右侧向左滑入 |
| `rotate` | 旋转进入 | 旋转并放大进入 |
| `flip` | 翻转进入 | 3D 翻转效果进入 |
| `bounce` | 弹跳进入 | 弹性动画进入 |

## 基本用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="openModal">打开弹窗</el-button>

    <!-- 自定义弹窗 -->
    <CustomModal
      v-model:visible="dialogVisible"
      title="弹窗标题"
      animation="fade-scale"
      size="medium"
      @close="handleClose"
    >
      <p>这里是弹窗内容</p>
      
      <template #footer>
        <el-button @click="closeModal">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </template>
    </CustomModal>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const dialogVisible = ref(false)

const openModal = () => {
  dialogVisible.value = true
}

const closeModal = () => {
  dialogVisible.value = false
}

const handleClose = () => {
  console.log('弹窗关闭')
}

const confirm = () => {
  // 处理确认逻辑
  closeModal()
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `visible` | `boolean` | `false` | 是否显示弹窗（支持 v-model） |
| `title` | `string` | `''` | 弹窗标题 |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | 弹窗尺寸 |
| `fullscreen` | `boolean` | `false` | 是否全屏显示 |
| `showClose` | `boolean` | `true` | 是否显示关闭按钮 |
| `closeOnClickOutside` | `boolean` | `true` | 点击遮罩是否关闭 |
| `closeOnEscape` | `boolean` | `true` | 按 ESC 键是否关闭 |
| `animation` | `string` | `'fade-scale'` | 动画类型 |

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| `update:visible` | 弹窗显示状态改变时触发 | `(value: boolean)` |
| `close` | 弹窗关闭时触发 | - |
| `open` | 弹窗打开时触发 | - |

## 插槽

| 插槽名 | 说明 |
|--------|------|
| `default` | 弹窗主体内容 |
| `header` | 自定义头部内容 |
| `footer` | 自定义底部内容 |

## 尺寸说明

- **small**: 400px 宽度，适合简单的确认对话框
- **medium**: 600px 宽度，适合一般的表单弹窗
- **large**: 800px 宽度，适合复杂的内容展示
- **fullscreen**: 全屏显示，适合大量内容或复杂操作

## 高级用法

### 动态切换动画

```vue
<template>
  <div>
    <el-button @click="openWithAnimation('bounce')">弹跳动画</el-button>
    <el-button @click="openWithAnimation('rotate')">旋转动画</el-button>
    
    <CustomModal
      v-model:visible="visible"
      :animation="currentAnimation"
      title="动态动画"
    >
      <p>当前动画：{{ currentAnimation }}</p>
    </CustomModal>
  </div>
</template>

<script setup>
const visible = ref(false)
const currentAnimation = ref('fade-scale')

const openWithAnimation = (animation) => {
  currentAnimation.value = animation
  visible.value = true
}
</script>
```

### 全屏弹窗

```vue
<CustomModal
  v-model:visible="visible"
  :fullscreen="true"
  animation="slide-down"
  title="全屏弹窗"
>
  <!-- 全屏内容 -->
</CustomModal>
```

### 无关闭按钮的弹窗

```vue
<CustomModal
  v-model:visible="visible"
  :show-close="false"
  :close-on-click-outside="false"
  :close-on-escape="false"
  title="必须操作的弹窗"
>
  <p>此弹窗只能通过底部按钮关闭</p>
  
  <template #footer>
    <el-button type="primary" @click="handleConfirm">确认</el-button>
  </template>
</CustomModal>
```

## 样式定制

组件使用了 CSS 变量和现代的动画效果，你可以通过覆盖 CSS 来自定义样式：

```scss
// 自定义背景模糊效果
.modal-backdrop {
  backdrop-filter: blur(8px) !important;
}

// 自定义弹窗圆角
.modal-container {
  border-radius: 16px !important;
}

// 自定义阴影
.modal-container {
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4) !important;
}
```

## 与 el-dialog 的对比

| 特性 | CustomModal | el-dialog |
|------|-------------|-----------|
| 动画效果 | 8 种丰富动画 | 基础淡入淡出 |
| 性能 | 使用 CSS3 动画，性能更好 | 较重的 JS 动画 |
| 自定义性 | 高度可定制 | 受限于组件设计 |
| 文件大小 | 轻量级 | 相对较重 |
| 浏览器兼容性 | 现代浏览器 | 更广泛兼容 |

## 注意事项

1. 组件使用了 `teleport` 将弹窗渲染到 `body` 元素下，确保层级正确
2. 动画效果依赖 CSS3，在老旧浏览器中可能不支持
3. 全屏模式下会覆盖整个视口，请谨慎使用
4. 建议在移动端使用时测试各种动画效果的表现

## 更新日志

### v1.0.0
- ✨ 初始版本发布
- ✨ 支持 8 种动画效果
- ✨ 支持多种尺寸配置
- ✨ 完善的事件和插槽支持
