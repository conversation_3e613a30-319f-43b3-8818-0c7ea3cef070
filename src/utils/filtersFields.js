// export function filtersFieldsFn(filtersFields, gridOptions) {
//   console.log(123);
//   return;
//   gridOptions.columns.map((decideRightItem) => {
//     if (!decideRightItem?.field) return;
//     if (filtersFields.includes(decideRightItem?.field)) {
//       const filters1 = [...new Set(gridOptions.data.map((item) => item[decideRightItem?.field]))].map((val) => ({
//         label: val,
//         value: val
//       }));
//       decideRightItem.filters = filters1;
//     }
//   });
// }
