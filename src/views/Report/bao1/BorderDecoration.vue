<template>
  <div class="border-decoration">
    <!-- 左上边框 -->
    <div class="t_line_box">
      <i class="t_l_line"></i>
      <i class="l_t_line"></i>
    </div>
    <!-- 右上边框 -->
    <div class="t_line_box">
      <i class="t_r_line"></i>
      <i class="r_t_line"></i>
    </div>
    <!-- 左下边框 -->
    <div class="t_line_box">
      <i class="l_b_line"></i>
      <i class="b_l_line"></i>
    </div>
    <!-- 右下边框 -->
    <div class="t_line_box">
      <i class="r_b_line"></i>
      <i class="b_r_line"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BorderDecoration'
}
</script>

<style scoped>
.border-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.t_line_box {
  position: absolute;
  width: 20px;
  height: 20px;
}

.t_line_box:nth-child(1) {
  top: 0;
  left: 0;
}

.t_line_box:nth-child(2) {
  top: 0;
  right: 0;
}

.t_line_box:nth-child(3) {
  bottom: 0;
  left: 0;
}

.t_line_box:nth-child(4) {
  bottom: 0;
  right: 0;
}

.t_l_line, .l_t_line, .t_r_line, .r_t_line,
.l_b_line, .b_l_line, .r_b_line, .b_r_line {
  position: absolute;
  background: #4b8df8;
}

.t_l_line, .t_r_line {
  width: 20px;
  height: 2px;
  top: 0;
}

.l_t_line, .r_t_line {
  width: 2px;
  height: 20px;
  left: 0;
}

.r_t_line {
  right: 0;
  left: auto;
}

.l_b_line, .r_b_line {
  width: 2px;
  height: 20px;
  bottom: 0;
}

.r_b_line {
  right: 0;
}

.b_l_line, .b_r_line {
  width: 20px;
  height: 2px;
  bottom: 0;
}

.b_r_line {
  right: 0;
}
</style>
