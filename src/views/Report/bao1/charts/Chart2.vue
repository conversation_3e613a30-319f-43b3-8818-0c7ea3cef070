<template>
  <div ref="chartRef" class="chart" style="width: 100%; height: 280px"></div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'Chart2',
  setup() {
    const chartRef = ref(null)
    let chartInstance = null
    let resizeObserver = null
    let resizeTimer = null

    const initChart = () => {
      if (!chartRef.value) return

      chartInstance = echarts.init(chartRef.value)

      const option = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['销售额','订单数'],
          textStyle: {
            color: '#fff'
          },
          top: '8%'
        },
        grid: {
          top: '40%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        color: ['#FF4949','#FFA74D','#FFEA51','#4BF0FF','#44AFF0','#4E82FF','#584BFF','#BE4DFF','#F845F1'],
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['2018年9月','2018年10月','2018年11月','2018年12月','2019年1月'],
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        },
        yAxis: {
          name: '',
          type: 'value',
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        },
        series: [
          {
            name: '销售额',
            type: 'line',
            data: [3961.88, 4233.63, 4183.14, 3633.01, 3704.47]
          },
          {
            name: '订单数',
            type: 'line',
            data: [3374.76, 3364.76, 3274.76, 3371.82, 3259.87]
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 防抖的 resize 处理函数
    const handleResize = () => {
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }
      resizeTimer = setTimeout(() => {
        if (chartInstance) {
          chartInstance.resize()
        }
      }, 100) // 100ms 防抖
    }

    // 初始化 ResizeObserver 监听容器大小变化
    const initResizeObserver = () => {
      if (typeof ResizeObserver !== 'undefined' && chartRef.value) {
        resizeObserver = new ResizeObserver(() => {
          handleResize()
        })
        resizeObserver.observe(chartRef.value)
      }
    }

    onMounted(async () => {
      await nextTick() // 确保 DOM 已渲染
      initChart()
      initResizeObserver()

      // 监听窗口大小变化
      window.addEventListener('resize', handleResize)

      // 监听浏览器缩放
      window.addEventListener('orientationchange', handleResize)

      // 延迟执行一次 resize，确保图表正确渲染
      setTimeout(() => {
        handleResize()
      }, 300)
    })

    onUnmounted(() => {
      // 清理定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }

      // 清理 ResizeObserver
      if (resizeObserver) {
        resizeObserver.disconnect()
      }

      // 清理图表实例
      if (chartInstance) {
        chartInstance.dispose()
      }

      // 移除事件监听器
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    })

    return {
      chartRef
    }
  }
}
</script>

<style scoped>
.chart {
  margin-top: 20px;
}
</style>
