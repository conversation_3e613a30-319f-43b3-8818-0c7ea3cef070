<template>
  <div ref="chartRef" class="chart" style="width: 100%; height: 280px"></div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'Chart1',
  setup() {
    const chartRef = ref(null)
    let chartInstance = null

    const initChart = () => {
      if (!chartRef.value) return

      chartInstance = echarts.init(chartRef.value)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: "{a} <br/>{b} : {c}件"
        },
        legend: {
          x: 'center',
          y: '15%',
          data: ['南宁市', '柳州市', '桂林市','梧州市', '北海市', '防城港市','钦州市','贵港市', '玉林市','百色市','贺州市','河池市','来宾市','崇左市'],
          icon: 'circle',
          textStyle: {
            color: '#fff',
          }
        },
        calculable: true,
        series: [{
          name: '',
          type: 'pie',
          radius: '45%',
          center: ['60%', '65%'],
          label: {
            normal: {
              show: true,
              formatter: '{b}{c}万件'
            },
            emphasis: {
              show: true
            }
          },
          labelLine: {
            normal: {
              show: true,
              length2: 1,
            },
            emphasis: {
              show: true
            }
          },
          data: [
            {
              value: 25097.63,
              name: '南宁市',
              itemStyle: {
                normal: {
                  color: '#f845f1'
                }
              }
            },
            {
              value: 4740.16,
              name: '柳州市',
              itemStyle: {
                normal: {
                  color: '#ad46f3'
                }
              }
            },
            {
              value: 2931.79,
              name: '桂林市',
              itemStyle: {
                normal: {
                  color: '#5045f6'
                }
              }
            },
            {
              value: 1174.27,
              name: '梧州市',
              itemStyle: {
                normal: {
                  color: '#4777f5'
                }
              }
            },
            {
              value: 1423.21,
              name: '北海市',
              itemStyle: {
                normal: {
                  color: '#44aff0'
                }
              }
            },
            {
              value: 1132.37,
              name: '防城港市',
              itemStyle: {
                normal: {
                  color: '#45dbf7'
                }
              }
            },
            {
              value: 929.50,
              name: '钦州市',
              itemStyle: {
                normal: {
                  color: '#f6d54a'
                }
              }
            },
            {
              value: 1555.20,
              name: '贵港市',
              itemStyle: {
                normal: {
                  color: '#f69846'
                }
              }
            },
            {
              value: 4881.52,
              name: '玉林市',
              itemStyle: {
                normal: {
                  color: '#ad46f3'
                }
              }
            },
            {
              value: 1114.72,
              name: '百色市',
              itemStyle: {
                normal: {
                  color: '#32C12E'
                }
              }
            },
            {
              value: 605.85,
              name: '贺州市',
              itemStyle: {
                normal: {
                  color: '#90F5AA'
                }
              }
            },
            {
              value: 596.23,
              name: '河池市',
              itemStyle: {
                normal: {
                  color: '#F46852'
                }
              }
            },
            {
              value: 419.44,
              name: '来宾市',
              itemStyle: {
                normal: {
                  color: '#eaf048'
                }
              }
            },
            {
              value: 1499.18,
              name: '崇左市',
              itemStyle: {
                normal: {
                  color: '#9ff048'
                }
              }
            }
          ]
        }]
      }

      chartInstance.setOption(option)
    }

    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)

      // 延迟执行一次 resize，确保图表正确渲染
      setTimeout(() => {
        handleResize()
      }, 100)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef
    }
  }
}
</script>

<style scoped>
.chart {
  margin-top: 20px;
}
</style>
