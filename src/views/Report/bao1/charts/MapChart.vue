<template>
  <div ref="chartRef" class="chart" style="width: 100%; height: 610px"></div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'MapChart',
  setup() {
    const chartRef = ref(null)
    let chartInstance = null

    const initChart = () => {
      if (!chartRef.value) return

      chartInstance = echarts.init(chartRef.value)
      
      // 广西各县市坐标数据
      const geoCoordMap = {
        '富川瑶族自治县': [111.1627,24.4913],
        '龙州县': [106.8545,22.3426],
        '南丹县': [107.5422,24.9753],
        '扶绥县': [107.9041,22.6349],
        '天峨县': [107.1737,24.9991],
        '大化瑶族自治县': [107.9981,23.7364],
        '乐业县': [106.5616,24.7858],
        '西林县': [105.0938,24.4895],
        '资源县': [110.6525,26.0424],
        '都安瑶族自治县': [108.1055,23.9312],
        '上林县': [108.6050,23.4320],
        '凌云县': [106.5615,24.3475],
        '东兰县': [107.3742,24.5107],
        '巴马瑶族自治县': [107.2596,24.1410],
        '田阳县': [106.9156,23.7356],
        '靖西市': [106.4176,23.1340],
        '大新县': [107.2012,22.8304],
        '三江侗族自治县': [109.6078,25.7831],
        '龙胜各族自治县': [110.0114,25.7980],
        '全州县': [111.0730,25.9286],
        '融安县': [109.3976,25.2246],
        '柳城县': [109.2446,24.6505],
        '忻城县': [108.6657,24.0661],
        '鹿寨县': [109.7517,24.4730],
        '宾阳县': [108.8103,23.2176],
        '兴业县': [109.8751,22.7363],
        '横县': [109.2614,22.6799],
        '灵山县': [109.2909,22.4165],
        '浦北县': [109.5569,22.2715],
        '东兴市': [107.9718,21.5479],
        '灌阳县': [111.1608,25.4893],
        '恭城瑶族自治县': [110.8283,24.8313],
        '荔浦县': [110.3981,24.4965],
        '昭平县': [110.8113,24.1694],
        '金秀瑶族自治县': [110.1894,24.1303],
        '桂平市': [110.0790,23.3941],
        '田东县': [107.1260,23.5972],
        '凤山县': [107.0422,24.5469],
        '环江毛南族自治县': [108.2583,24.8260],
        '马山县': [108.1770,23.7081],
        '罗城仫佬族自治县': [108.9046,24.7773],
        '德保县': [106.6155,23.3234],
        '融水苗族自治县': [109.2563,25.0662],
        '天等县': [107.1436,23.0813],
        '隆林县': [105.3438,24.7706],
        '那坡县': [105.8334,23.3871],
        '平果县': [107.5898,23.3293]
      }

      // 电商进农村示范县数据
      const data = [
        {
          name: '富川瑶族自治县',
          value: [50,'电商进农村示范县：<br/>项目承建企业24家<br/>电商服务站目前数量24个站点<br/>广西金岸科技有限公司等企业均以优秀标准验收<br/>主要推广特产富川脐橙...']
        },
        {
          name: '龙州县',
          value: [50,'电商进农村示范县：<br/>项目承建企业24家<br/>电商服务站目前数量24个站点<br/>广西金岸科技有限公司等企业均以优秀标准验收<br/>主要推广特产富川脐橙...']
        },
        {
          name: '西林县',
          value: [50,'电商进农村示范县：<br/>项目承建企业2家<br/>电商服务站目前数量94个站点<br/>广西乐村淘科技有限公司、广西国际电子商务中心<br/>主要推广的产品有西林沙糖桔、麻鸭、姜晶等地理标志保护产品']
        },
        {
          name: '巴马瑶族自治县',
          value: [50,'项目承建企业1家<br/>电商服务站目前数量85个站点<br/>参加电商培训人数1500人<br/>特色粮经作物：包括蚕桑、火麻、龙骨花、中药材、红薯、马铃薯、小杂粮等。<br/>主要承建内容:县级服务中心建设、乡镇级服务站、村级服务点建设']
        },
        {
          name: '靖西市',
          value: [50,'项目承建企业1家<br/>电商服务站目前数量60个站点<br/>参加电商培训人数1500人<br/>特色产品：靖西绣球、靖西壮锦、靖西东利大香儒<br/>主要承建内容:靖西各镇乡村服务站点建设']
        },
        {
          name: '柳城县',
          value: [50,'项目承建企业2家<br/>电商服务站目前数量125个站点<br/>参加电商培训人数5000人<br/>特色产品：新味嘉木瓜丝、寨隆壮方红糖<br/>主要承建内容:农村电子商务公共服务体系村级服务点建设。']
        },
        {
          name: '浦北县',
          value: [50,'项目承建企业2家<br/>电商服务站目前数量161个站点<br/>参加电商培训人数8572人<br/>特色产品：浦北扁柑、浦北黑猪、官垌鱼、浦北黑叶荔<br/>主要承建内容:建设浦北县电子商务公共服务中心，<br/>县级公共服务中心与品牌推与展示区、创业孵化区、培训区集聚发展。']
        },
        {
          name: '东兴市',
          value: [50,'项目承建企业3家<br/>电商服务站目前数量34个站点<br/>特色产品：东兴红姑娘红薯、东兴黄皮果、京族二宝<br/>主要承建内容:建设东兴市电子商务进农村服务中心。<br/>含公共服务区、多平台运营服务区等。']
        }
      ]

      const max = 480
      const min = 9
      const maxSize4Pin = 50
      const minSize4Pin = 20

      const convertData = function (data) {
        const res = []
        for (let i = 0; i < data.length; i++) {
          const geoCoord = geoCoordMap[data[i].name]
          if (geoCoord) {
            res.push({
              name: data[i].name,
              value: geoCoord.concat(data[i].value)
            })
          }
        }
        return res
      }

      // 注册广西地图（这里需要广西地图的GeoJSON数据）
      // 由于没有实际的地图数据，我们创建一个简化的配置
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function(result) {
            return result.name + '<br />' + (result.value[3] || '暂无详细信息')
          }
        },
        geo: {
          zoom: 1.2,
          show: true,
          map: '广西',
          mapType: '广西',
          label: {
            normal: {
              show: true,
              textStyle: { color: "#4bf316" }
            },
            emphasis: {
              show: true,
              textStyle: { color: '#fff' }
            }
          },
          roam: true,
          itemStyle: {
            normal: {
              borderColor: 'rgba(147, 235, 248, 1)',
              borderWidth: 2,
              areaColor: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(175,238,238, 0)'
                }, {
                  offset: 1,
                  color: 'rgba(47,79,79, .2)'
                }],
                globalCoord: false
              },
              shadowColor: 'rgba(128, 217, 248, 1)',
              shadowOffsetX: -2,
              shadowOffsetY: 2,
              shadowBlur: 10
            },
            emphasis: {
              areaColor: '#389BB7',
              borderWidth: 0
            }
          }
        },
        series: [
          {
            name: '电商进农村示范县',
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol: 'pin',
            symbolSize: function(val) {
              const a = (maxSize4Pin - minSize4Pin) / (max - min)
              let b = minSize4Pin - a * min
              b = maxSize4Pin - a * max
              return a * val[2] + b
            },
            label: {
              normal: {
                formatter: '{b}',
                show: true,
                textStyle: {
                  color: '#fff',
                  fontSize: 10
                }
              }
            },
            itemStyle: {
              normal: {
                color: 'red'
              }
            },
            zlevel: 6,
            data: convertData(data)
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            data: convertData(data.sort(function (a, b) {
              return b.value - a.value
            }).slice(0, 8)),
            symbolSize: function (val) {
              return val[2] / 10
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'stroke'
            },
            hoverAnimation: true,
            itemStyle: {
              normal: {
                color: '#05C3F9',
                shadowBlur: 10,
                shadowColor: '#05C3F9'
              }
            },
            zlevel: 1
          }
        ]
      }

      chartInstance.setOption(option)
    }

    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef
    }
  }
}
</script>

<style scoped>
.chart {
  margin-top: 20px;
}
</style>
