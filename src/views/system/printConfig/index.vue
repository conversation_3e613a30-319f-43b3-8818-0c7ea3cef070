<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="设备ip" prop="deviceIp">
              <el-input v-model="queryParams.deviceIp" placeholder="请输入设备ip" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="打印机ip" prop="printerIp">
              <el-input v-model="queryParams.printerIp" placeholder="请输入打印机ip" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:printConfig:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:printConfig:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:printConfig:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:printConfig:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="printConfigList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="打印配置id" align="center" prop="mesPrintConfigId" v-if="false" />
        <el-table-column label="设备ip" align="center" prop="deviceIp" />
        <el-table-column label="打印机ip" align="center" prop="printerIp" />
        <el-table-column label="打印机端口" align="center" prop="port" />
        <el-table-column label="打印机型号" align="center" prop="printerModel" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:printConfig:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:printConfig:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改打印机配置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="printConfigFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="设备ip" prop="deviceIp">
          <!-- <el-input v-model="form.deviceIp" placeholder="请输入设备ip" /> -->
          <el-input v-model="form.deviceIp" placeholder="请输入设备ip" clearable>
            <template #append> <el-button type="primary" @click="getIpFn">获取 ip</el-button></template>
          </el-input>
        </el-form-item>
        <el-form-item label="打印机ip" prop="printerIp">
          <el-input v-model="form.printerIp" placeholder="请输入打印机ip" />
        </el-form-item>
        <el-form-item label="打印机端口" prop="port">
          <el-input v-model="form.port" placeholder="请输入打印机端口" :disabled="true" />
        </el-form-item>
        <el-form-item label="打印机型号" prop="printerModel">
          <el-input v-model="form.printerModel" placeholder="请输入打印机型号" :disabled="true" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PrintConfig" lang="ts">
import { listPrintConfig, getPrintConfig, delPrintConfig, addPrintConfig, updatePrintConfig, getUserIp } from '@/api/system/printConfig';
import { PrintConfigVO, PrintConfigQuery, PrintConfigForm } from '@/api/system/printConfig/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const printConfigList = ref<PrintConfigVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const printConfigFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PrintConfigForm = {
  deviceIp: undefined,
  printerIp: undefined,
  port: 9100,
  printerModel: 'XP-420B'
};
const data = reactive<PageData<PrintConfigForm, PrintConfigQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deviceIp: undefined,
    printerIp: undefined,
    params: {}
  },
  rules: {
    deviceIp: [{ required: true, message: '设备ip不能为空', trigger: 'blur' }],
    printerIp: [{ required: true, message: '打印机ip不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询打印机配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPrintConfig(queryParams.value);
  printConfigList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  printConfigFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PrintConfigVO[]) => {
  ids.value = selection.map((item) => item.mesPrintConfigId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加打印机配置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PrintConfigVO) => {
  reset();
  const _mesPrintConfigId = row?.mesPrintConfigId || ids.value[0];
  const res = await getPrintConfig(_mesPrintConfigId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改打印机配置';
};

/** 提交按钮 */
const submitForm = () => {
  printConfigFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.mesPrintConfigId) {
        await updatePrintConfig(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addPrintConfig(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PrintConfigVO) => {
  const _mesPrintConfigIds = row?.mesPrintConfigId || ids.value;
  await proxy?.$modal.confirm('是否确认删除打印机配置编号为"' + _mesPrintConfigIds + '"的数据项？').finally(() => (loading.value = false));
  await delPrintConfig(_mesPrintConfigIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/printConfig/export',
    {
      ...queryParams.value
    },
    `printConfig_${new Date().getTime()}.xlsx`
  );
};
const getIpFn = () => {
  // proxy?.getIp();
  getUserIp().then((res) => {
    console.log(res);
    form.value.deviceIp = res.msg;
  });
};
onMounted(() => {
  getList();
});
</script>
