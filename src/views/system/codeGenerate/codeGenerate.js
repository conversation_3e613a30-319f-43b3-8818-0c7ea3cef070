export function codeGenerate() {
  const inputFilterRender = reactive({
    name: 'VxeInput'
  });
  const systemCodeGenerate = reactive({
    id: 'systemCodeGenerateId ',
    height: '100%',
    emptyText: '没有更多数据了！',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
        mode: 'modal',
        storage: true,
        modalOptions: {
          height: '500px',
          className: 'decideRight-modal'
        }
      },
      toolbarConfig: {
        custom: true
      },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [
      {
        width: '150',
        title: '规则名称',
        field: 'ruleName',
        align: 'center',
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
     
      {
        title: "编码类型",
        field: "type",
        align: 'center',
        filters: [{ data: '' }],
        filterRender: inputFilterRender
        
      },
      {
        title: "表注释名称",
        field: "commentName",
        align: 'center',
        filters: [{ data: '' }],
        filterRender: inputFilterRender
        
      },
      {
        title: "前缀",
        field: "prefix",
        align: 'center',
        filters: [{ data: '' }],
        filterRender: inputFilterRender
        
      },
      {
        title: "序号长度",
        field: "seriationLen",
        
        
      },
      {
        title: "补填字符",
        field: "addInChar",
        align: 'center',
        filters: [{ data: '' }],
        filterRender: inputFilterRender
        
      },
      // {
      //   title: "创建者",
      //   field: "creator",
      //   align: 'center',
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
        
      // },
      // {
      //   title: "创建时间",
      //   field: "createTime",
      //   align: 'center',
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
        
      // },
      {
        title: "后缀",
        field: "postfix",
        align: 'center',
        filters: [{ data: '' }],
        filterRender: inputFilterRender
        
      },
      {
        title: "备注",
        field: "remark",
        align: 'center',
        filters: [{ data: '' }],
        filterRender: inputFilterRender
        
      },
      // {
      //   title: "最后修改人",
      //   field: "lastModifier",
      //   align: 'center',
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
        
      // },
      // {
      //   title: "最后修改日期",
      //   field: "lastModifiedDate",
      //   align: 'center',
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
        
      // },
      {
        title: "操作",
        width: '150',
        align: 'center',
        slots: { default: 'default_edit' }
      },
    ],
    data: []
  });
  
  return {
    systemCodeGenerate,
  };
}
