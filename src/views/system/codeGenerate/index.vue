<template>
  <div class="app-container white">
    <el-button type="primary" plain :icon="CirclePlus" @click="Add" v-hasPermi="[`system:codeGenerateRule:edit`]">新增</el-button>

    <div class="table_box">
      <div class="table-box">
        <vxe-grid class="mylist-table-systemCodeGenerate" ref="systemCodeGenerateRef" v-bind="systemCodeGenerate"  @cell-dblclick="dblclick">
          <template #default_edit="{ row }">
            <el-button type="primary" :icon="Edit" circle  @click="edit(row)"/>
            <el-button type="danger" :icon="Delete" circle @click="handleDel(row)"/>
          </template>
        </vxe-grid>
      </div>
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[30, 60, 90, 120]"
          layout="total, sizes, prev, pager,"
          :total="queryParams.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>
<script  setup>
import {
  Delete,
  Edit,
} from '@element-plus/icons-vue'
const permissionObj = {
  edit: 'system:codeGenerateRule:edit',
  remove: 'system:codeGenerateRule:remove',
  query: 'system:codeGenerateRule:query'
};
import { CirclePlus } from '@element-plus/icons-vue';
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter();
import { codeGenerate } from './codeGenerate';
import { getcodeGenerateRule, delcodeGenerateRule } from '@/api/system/codeGenerate/codeGenerate.js';
const { proxy } = getCurrentInstance();
const { systemCodeGenerate } = codeGenerate();
const pagination = reactive({ total: 0 });
const form = reactive({ pageNum: 1, pageSize: 10 });
const data = reactive({
  tableData: [],
  tableObj: {}
});
const queryParams = ref({ schedule: '', pageNum: 1, pageSize: 30, total: 0 });
const dblclick = (row) => {
  router.push(`/system/codeGenerateEdit?id=${row.id}`);
};
const handleEdit = (id) => {
  router.push(`/system/codeGenerateEdit?id=${id}`);
};

const handleCurrentChange = (val) => {
  form.pageNum = val;
  getGenerateListFn();
};
const handleSizeChange = (val) => {
  form.pageSize = val;
  getGenerateListFn();
};
const Add = () => {
  router.push('/system/codeGenerateEdit');
};
onMounted(() => {
  getGenerateListFn();
});
const getGenerateListFn = () => {
  getcodeGenerateRule(queryParams.value).then((res) => {
    systemCodeGenerate.data = res.rows;
    // data.tableData = res.rows;
    // ({ total: pagination.total, current: pagination.current, size: pagination.size } = res.data)
  });
};
const edit = (row) => {
    router.push(`/system/codeGenerateEdit?id=${row.id}`);
};
const handleDel = (row) => {
  console.log(row)
  delcodeGenerateRule(row.id).then((res) => {
    getGenerateListFn();
    proxy?.$modal.msgSuccess('删除成功');
  });
}
</script>
<style lang='scss' scoped>
.table_box {
  height: calc(100vh - 160px);
  // background-color: aqua;
  display: flex;
  flex-direction: column;
}

.pagination {
  flex-shrink: 0;
  display: flex;
  justify-content: right;
  align-items: center;
  margin-right: 10px;
}

.table-box {
  flex-grow: 1;
  overflow: auto;
}
</style>
