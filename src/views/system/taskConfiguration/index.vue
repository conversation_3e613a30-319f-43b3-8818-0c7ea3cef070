<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div>
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-medium">明细信息:</h2>
        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="addNewRow">
          <el-icon class="mr-1"><Plus /></el-icon>
          添加
        </el-button>
      </div>
      <el-table :data="tableData" border height="700px">
        <el-table-column label="顺序号" width="80">
          <template #default="{ $index, row }">
            <div class="flex items-center space-x-1 justify-center">
              <span class="mr-2">{{ row.serialNum }}</span>
              <el-icon class="cursor-pointer text-gray-400 hover:text-blue-500" @click="moveUp($index)"><ArrowUp /></el-icon>
              <el-icon class="cursor-pointer text-gray-400 hover:text-blue-500" @click="moveDown($index)"><ArrowDown /></el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="任务编码" width="200">
          <template #default="{ row }">
            <el-input v-model="row.code" placeholder="请输入任务编码" class="w-full" />
          </template>
        </el-table-column>
        <el-table-column label="任务名称" width="200">
          <template #default="{ row }">
            <el-input v-model="row.taskName" placeholder="请输入任务名称" class="w-full" />
          </template>
        </el-table-column>
        <el-table-column label="任务结束" width="100">
          <template #default="{ row }">
            <el-checkbox v-model="row.endMandate" @change="(value) => handleResultChange(value, row)" :true-value="1" :false-value="0" />
          </template>
        </el-table-column>
        <el-table-column label="配置时间" width="100">
          <template #default="{ row }">
            <el-checkbox v-model="row.configureTime" :true-value="1" :false-value="0" />
          </template>
        </el-table-column>
        <el-table-column label="角色" width="250">
          <template #default="scoped">
            <span v-if="!scoped.row.roleId" style="cursor: pointer" @click="getRoleListFn(scoped.row)"
              >选择角色 <el-button type="primary" link @click="getRoleListFn(scoped.row)">重选</el-button></span
            >
            <span v-else
              >{{ scoped.row.roleName }}<el-button type="primary" link @click="getRoleListFn(scoped.row)">重选</el-button>
              <el-button link type="danger" @click="(scoped.row.roleId = ''), (scoped.row.roleName = '')">删除</el-button></span
            >
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <template #default="{ row }">
            <el-input v-model="row.remark" placeholder="请输入备注" class="w-full" />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="keep_box"><el-button type="primary" @click="keep">保存</el-button></div>
    <el-dialog v-model="roleListIsShow" title="角色列表" width="500">
      <el-table :data="roleListTable" height="600px" @cell-dblclick="roleListDblclick">
        <el-table-column property="roleName" label="角色" />
        <!-- <el-table-column property="name" label="Name"  /> -->
        <!-- <el-table-column property="address" label="Address" /> -->
      </el-table>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import { Plus, ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { getRoleList, addOrUpdateMesTaskConfiguration, gettaskConfigurationlist,getRoleList2 } from '@/api/system/taskConfiguration/index';
const tableData = ref([]);
const addNewRow = () => {
  const maxSortNo = Math.max(...tableData.value.map((row) => row.serialNum), 0);
  tableData.value.push({
    serialNum: maxSortNo + 1
  });
};
const moveUp = (index) => {
  if (index > 0) {
    const temp = { ...tableData.value[index] };
    tableData.value[index] = { ...tableData.value[index - 1] };
    tableData.value[index - 1] = temp;
    updateSortNumbers();
  }
};
const moveDown = (index) => {
  if (index < tableData.value.length - 1) {
    const temp = { ...tableData.value[index] };
    tableData.value[index] = { ...tableData.value[index + 1] };
    tableData.value[index + 1] = temp;
    updateSortNumbers();
  }
};
const handleResultChange = (value, currentRow) => {
  if (value) {
    // 取消其他行的选中状态
    tableData.value.forEach((row) => {
      if (row !== currentRow) {
        row.result = false;
      }
    });
  }
};
const updateSortNumbers = () => {
  tableData.value.forEach((row, index) => {
    row.serialNum = index + 1;
  });
};
let actvieRow = ref();
const getRoleListFn = (row) => {
  actvieRow.value = row;
  getRoleList().then((res) => {
    // console.log(res);
    roleListTable.value = res.rows;
    roleListIsShow.value = true;
  });
  // getRoleList2().then(res=>{
  //   console.log(res)
  // })
};
const roleListTable = ref();
const roleListIsShow = ref(false);
const roleListDblclick = (row) => {
  actvieRow.value.roleId = row.roleId;
  actvieRow.value.roleName = row.roleName;
  roleListIsShow.value = false;
};
const keep = () => {
  addOrUpdateMesTaskConfiguration(tableData.value).then((res) => {
    ElMessage({
      message: '保存成功',
      type: 'success'
    });
    gettaskConfigurationlistFn();
  });
};
const gettaskConfigurationlistFn = () => {
  gettaskConfigurationlist().then((res) => {
    tableData.value = res.rows;
  });
};
onMounted(() => {
  gettaskConfigurationlistFn();
});
</script>
    <style scoped>
.el-table :deep(.el-table__header) {
  background-color: #f5f7fa;
}
.el-table :deep(.el-input__wrapper) {
  box-shadow: none !important;
}
.el-table :deep(.el-checkbox__inner) {
  border-radius: 2px;
  width: 20px;
  height: 20px;
}
.el-table :deep(.el-checkbox__inner::after) {
  width: 6px;
  height: 10px;
  left: 6px;
  top: 2px;
}
.p-8 {
  padding: 1rem;
}
.min-h-screen {
  min-height: calc(100vh - 90px) !important;
  background-color: #fff;
}
.keep_box {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  /* margin: 0 auto;
    df */
  display: flex;
  justify-content: center;
}
* {
  font-size: 16px !important;
}
:deep .cell {
  font-size: 16px;
}
</style>
    