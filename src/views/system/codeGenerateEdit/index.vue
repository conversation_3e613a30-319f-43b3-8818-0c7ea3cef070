<template>
  <div class="fa-box">
    <div class="detail_box">基础信息:</div>
    <div class="content_box" style="margin-top: 10px">
      <el-form :model="form" label-width="auto" ref="ruleFormRef" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="规则名称:" prop="ruleName">
              <el-input v-model="form.ruleName" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="编码类型:" prop="type">
              <el-input v-model="form.type" clearable disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建者:">
              <el-input v-model="form.createBy" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="表注释名称:" prop="commentName">
              <el-input v-model="form.commentName" clearable disabled />
            </el-form-item>
          </el-col>
       
          <el-col :span="8">
            <el-form-item label="后缀:" prop="postfix">
              <el-button type="primary" plain @click="postfixOpen">后缀</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间:">
              <el-input v-model="form.createTime" clearable disabled />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="前缀:" prop="prefix">
              <el-input v-model="form.prefix" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最后修改人:">
              <el-input v-model="form.lastModifier" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="序号位数:" prop="seriationLen">
              <el-input v-model="form.seriationLen" clearable type="number" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="补填字符:" prop="addInChar">
              <el-input v-model="form.addInChar" clearable show-word-limit />
            </el-form-item>
          </el-col>
        
         
          <el-col :span="8">
            <el-form-item label="最后修改时间:">
              <el-input v-model="form.lastModifiedDate" clearable disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:" prop="remark">
              <el-input v-model="form.remark" clearable type="textarea" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="table-box" style="display: flex; ">
      <el-table ref="multipleTableRef11" stripe :data="data.codeGenerateList" style="width: 90%;height:100%" :scrollbar-always-on="true">
        <el-table-column width="55">
          <template #header>
            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"></el-checkbox>
          </template>

          <template #default="scope">
            <el-checkbox v-model="scope.row.elected" @change="handleCheckedCitiesChange" :true-label="1" :false-label="0"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column type="index" property="serialNum" label="排序" width="80" />
        <el-table-column property="formatLen" label="格式化长度">
          <template #default="scope">
            <el-input v-model="scope.row.formatLen" placeholder=""  />
          </template>
        </el-table-column>
        <el-table-column property="formatString" label="格式化" width="155">
          <template #default="scope">
            <el-select v-model="scope.row.formatString" placeholder="" >
              <el-option v-for="item in optionList.formatString" :key="item.value" :label="item.dictLabel" :value="item.dictValue" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column property="columnComment" label="名称">
          <template #default="scope">
            <el-input v-model="scope.row.columnComment" placeholder=""  />
          </template>
        </el-table-column>
        <el-table-column property="columnType" label="操作">
          <template #default="scope">
            <el-button type="primary" link @click="moveUp(scope.$index)" :disabled="scope.$index == 0">上移</el-button>
            <el-button type="primary" link @click="moveDown(scope.$index)" :disabled="scope.$index == data.codeGenerateList.length - 1"
              >下移</el-button
            >
            <el-button type="primary" plain @click="ziDuan(scope.row, scope.$index)">字段配置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-left: 10px">
        <el-button type="primary" plain @click="isShow" v-hasPermi="[`system:codeGenerateRule:edit`]">选择数据表</el-button>
      </div>
      <el-dialog v-model="dialogshow" title="数据表" width="50%" draggable>
        <span>
          <div >
            <el-table ref="multipleTableRef" :data="tableList" style="height:600px" @cell-dblclick="tableDbClick" border>
              <el-table-column type="index" width="70" label="序号" />
              <el-table-column label="表名称" prop="tableName" />
              <el-table-column property="tableComment" label="表描述" />
              <el-table-column property="className" label="实体" show-overflow-tooltip />
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              v-model:current-page="pagination.current"
              :page-size="dataForm.pageSize"
              :page-sizes="[10, 20, 30, 40]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
            />
          </div>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogshow = false">取消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 后缀弹窗 -->
      <el-dialog v-model="postfixShow" title="后缀" width="50%" draggable>
        <span>
          <span>
            <div class="selectRadio">
              <el-radio-group v-model="postfix.type">
                <el-radio :label="1" @change="handleRadioChange(1)">表格</el-radio>
                <el-radio :label="2" @change="handleRadioChange(2)">字典</el-radio>
                <el-radio :label="3" @change="handleRadioChange(3)">数组字段</el-radio>
                <el-radio :label="4" @change="handleRadioChange(4)">字符串</el-radio>
              </el-radio-group>
            </div>
            <div style="margin-top: 10px; display: flex; justify-content: space-around; gap: 10px">
              <div v-if="peizhiShowArr.indexOf(stringShow) != -1">
                <span>表名称:</span>
                <el-input v-model="postfix.tableName" placeholder="请输入表名称" :style="{ width: '100%' }" />
              </div>
              <div v-if="peizhiShowArr.indexOf(stringShow) != -1">
                <span>字段名称:</span>
                <el-input v-model="postfix.columnName" placeholder="请输入字段名称" :style="{ width: '100%' }" />
              </div>
              <div v-if="peizhiShowArr.indexOf(stringShow) != -1">
                <span>条件字段:</span>

                <el-input v-model="postfix.conditionColumn" placeholder="请输入条件字段" :style="{ width: '100%' }" />
              </div>
              <div v-if="stringShow == 2">
                <span>字典类型:</span>
                <el-input v-model="postfix.dictType" placeholder="请输入字典类型" :style="{ width: '100%' }" />
              </div>
              <div v-if="peizhiShowArr.indexOf(stringShow) != -1">
                <span>表格字段</span>

                <el-select
                  v-model="selectedId"
                  placeholder="请选择表格字段"
                  :style="{ width: '100%' }"
                  @change="handleSelectChange"
                  :value-key="'id'"
                  :collapse-tags="true"
                >
                  <el-option v-for="item in postfixList" :key="item.id" :label="item.columnComment" :value="item.id" />
                </el-select>
              </div>
              <div v-if="stringShow == 4">
                <span>字符串:</span>
                <el-input v-model="postfix.value" placeholder="请输入数据值" :style="{ width: '100%' }" />
              </div>
            </div>
            <div style="display: flex; margin-top: 10px" v-if="stringShow == 3">
              <span>分类数量:</span>
              <div>
                <el-input-number
                  v-model="postfix.categoryNum"
                  :style="{ width: '100%' }"
                  :min="1"
                  :max="999"
                  controls-position="right"
                  @change="handleChange2"
                />
              </div>
            </div>
            <div class="table_box" v-if="stringShow == 3">
              <el-table ref="multipleTableRef11" stripe :data="postfix.categoryConfiguration" style="width: 100%" :scrollbar-always-on="true">
                <el-table-column type="index" property="serialNum" label="排序" width="130" />
                <el-table-column property="formatLen" label="格式化长度">
                  <template #default="scope">
                    <el-input v-model="scope.row.formatLen" placeholder="默认为完整长度" />
                  </template>
                </el-table-column>
                <el-table-column property="formatString" label="格式化" width="155">
                  <template #default="scope">
                    <el-select v-model="scope.row.formatString" placeholder="" >
                      <el-option v-for="item in optionList.formatString" :key="item.value" :label="item.dictLabel" :value="item.dictValue" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column property="columnType" label="操作">
                  <template #default="scope">
                    <el-button type="danger" :disabled="scope.$index == 0" @click="del2(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </span>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="postfixHeti">确认</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog v-model="columnConfigurationShow" title="字段设置" width="50%" draggable>
        <span>
          <div class="selectRadio">
            <el-radio-group v-model="columnConfiguration.type">
              <el-radio :label="1" @change="peizhiRadioChange(1)">表格</el-radio>
              <el-radio :label="2" @change="peizhiRadioChange(2)">字典</el-radio>
              <el-radio :label="3" @change="peizhiRadioChange(3)">数组字段</el-radio>
              <el-radio :label="0" @change="peizhiRadioChange(0)">无</el-radio>
            </el-radio-group>
          </div>
          <div style="margin-top: 10px; display: flex; justify-content: space-around; gap: 10px">
            <div v-if="peizhiShowArr.indexOf(peizhiShow) != -1">
              <span>表名称:</span>
              <el-input v-model="columnConfiguration.tableName" placeholder="请输入表名称" :style="{ width: '100%' }" />
            </div>
            <div v-if="peizhiShowArr.indexOf(peizhiShow) != -1">
              <span>字段名称:</span>
              <el-input v-model="columnConfiguration.columnName" placeholder="请输入字段名称" :style="{ width: '100%' }" />
            </div>
            <div v-if="peizhiShowArr.indexOf(peizhiShow) != -1">
              <span>条件字段:</span>

              <el-input v-model="columnConfiguration.conditionColumn" placeholder="请输入条件字段" :style="{ width: '100%' }" />
            </div>
            <div v-if="peizhiShow == 2">
              <span>字典类型:</span>
              <el-input v-model="columnConfiguration.dictType" placeholder="请输入字典类型" :style="{ width: '100%' }" />
            </div>
          </div>
          <div style="display: flex; margin-top: 10px" v-if="peizhiShow == 3">
            <span>分类数量:</span>
            <div>
              <el-input-number
                v-model="columnConfiguration.categoryNum"
                :style="{ width: '100%' }"
                :min="1"
                :max="999"
                controls-position="right"
                @change="handleChange"
              />
            </div>
          </div>
          <div class="table_box" v-if="peizhiShow == 3">
            <el-table
              ref="multipleTableRef11"
              stripe
              :data="columnConfiguration.categoryConfiguration"
              style="width: 100%"
              :scrollbar-always-on="true"
            >
              <el-table-column type="index" property="serialNum" label="排序" width="130" />
              <el-table-column property="formatLen" label="格式化长度">
                <template #default="scope">
                  <el-input v-model="scope.row.formatLen" placeholder="默认为完整长度" />
                </template>
              </el-table-column>
              <el-table-column property="formatString" label="格式化" width="155">
                <template #default="scope">
                  <el-select v-model="scope.row.formatString" placeholder="" >
                    <el-option v-for="item in optionList.formatString" :key="item.value" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column property="columnType" label="操作">
                <template #default="scope">
                  <el-button type="danger" :disabled="scope.$index == 0" @click="del(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="peizhiZI">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
    <div class="save_box" style="display: flex; justify-content: center;">
      <el-button type="primary" @click="keep(ruleFormRef)" class="cenbtn" v-hasPermi="[`system:codeGenerateRule:edit`]">保存</el-button>
    </div>
  </div>
</template>
<script setup>
let peizhiShowArr = [1, 2, 3];
import { onMounted, reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  putcodeGenerateRule,
  getTableList,
  TableListdetail,
  getTableColumnList, //弹窗查询具体哪条数据
  codeGenerateRuleAdd, //新增
  getcodeGenerateRule
} from '@/api/system/codeGenerate/codeGenerate.js';
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const id = route.query.id;
let pagination = reactive({ total: 0 });
const form = ref({ addInChar: 0 });
const dialogshow = ref(false);
const msg = inject('message');
const data = reactive({
  codeGenerateList: []
});
const ruleFormRef = ref();
const rules = reactive({
  seriationLen: [{ required: true, message: '请填写序号位数', trigger: 'blur' }],
  addInChar: [{ required: true, message: '请填写补填字符', trigger: 'blur' }]
});
const dataForm = reactive({
  pageNum: 1,
  pageSize: 10
});
const filteredLength = ref(0);
const getTableListFn = () => {
  getTableList(dataForm).then((res) => {
    pagination.total = res.total;
    tableList.value = [];
    tableList.value = res.rows;
    
  });
};
const tableList = ref([]);
const isShow = () => {
  getTableListFn();
  dialogshow.value = true;
};
const handleCurrentChange = (num) => {
  dataForm.pageNum = num;
  getTableListFn();
};
const handleSizeChange = (num) => {
  dataForm.pageSize = num;
  getTableListFn();
};

const optionList = reactive({
  formatString: [
    {
      dictValue: 1,
      dictLabel: '-{0}'
    },
    {
      dictValue: 2,
      dictLabel: '-{0}-'
    },
    {
      dictValue: 3,
      dictLabel: '{0}-'
    },
    {
      dictValue: 0,
      dictLabel: ''
    }
  ]
});

const tableDbClick = async(row) => {
  // console.log(row.tableComment, 333)
  await getcodeGenerateRule({ pageNum: 1, pageSize: 999 }).then((res) => {
    for (let i = 0; i < res.rows.length; i++) {
      if (row.tableComment == res.rows[i].commentName) {
        proxy?.$modal.msgWarning('请勿重复生成');
        return;
      }
    }
  });
  // return
  await getTableColumnList(row.tableId).then((res) => {
    if (row.tableComment !== form.value.commentName) {
      data.codeGenerateList = res.rows;
      postfixList.value = data.codeGenerateList;
      postfixList.value.map((item) => {
        item.columnId = Number(item.columnId);
        item.tableId=Number(item.tableId)
      });
      dialogshow.value = false;
      form.value.type = row.className;
      form.value.commentName = row.tableComment;
    } else {
      proxy?.$modal.msgWarning('请勿重复生成');
    }
  });
};

onMounted(() => {
  if (id) {
    TableListdetailFn();
  }
});

const moveUp = (index) => {
  const temp = data.codeGenerateList[index];
  data.codeGenerateList.splice(index, 1);
  data.codeGenerateList.splice(index - 1, 0, temp);
};
const moveDown = (index) => {
  const temp = data.codeGenerateList[index];
  data.codeGenerateList.splice(index, 1);
  data.codeGenerateList.splice(index + 1, 0, temp);
};
const selectedRows = ref([]);
const multipleTableRef11 = ref();
const postfixList = ref([]);
const TableListdetailFn = async () => {
  await TableListdetail(id).then((res) => {
    form.value = res.data;
    if (res.data.postfix) {
      postfix = form.value.postfix;
      selectedId.value = postfix.column?.id;
      console.log(postfix);
    } else {
      postfix = reactive({
        column: {
          className: '',
          codeGenerateId: '',
          columnComment: '',
          columnConfiguration: '',
          columnId: 0,
          columnType: '',
          createTime: '',
          creator: '',
          elected: 0,
          formatLen: 0,
          formatString: 0,
          id: '',
          javaField: '',
          lastModifiedDate: '',
          lastModifier: '',
          params: {},
          remark: '',
          serialNum: 0
        },
        categoryConfiguration: [],
        value: '',
        type: 0
      });
    }

    data.codeGenerateList = res.data.codeGenerateColumnList;
    postfixList.value = res.data.codeGenerateColumnList;
    if (res.data.codeGenerateColumnList) {
      let num = 0;
      res.data.codeGenerateColumnList.map((item) => {
        if (item.elected == 1) {
          num += 1;
        }
      });
      checkAll.value = num === data.codeGenerateList.length;
      isIndeterminate.value = num > 0 && num < data.codeGenerateList.length;
    }
  });
  multipleTableRef11.value.clearSelection();
  let filterArr = data.codeGenerateList.filter((item) => item.elected == 1);
  filterArr.forEach((row) => {
    multipleTableRef11.value.toggleRowSelection(row, undefined);
  });
};

const keep = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      data.codeGenerateList.map((item, index) => {
        item.serialNum = index + 1;
      });
      if (id) {
        if (postfix) {
          form.value.postfix = postfix;
          console.log(form.value.postfix);
        }
        form.value.seriationLen = parseInt(form.value.seriationLen);
        form.value.codeGenerateColumnList = data.codeGenerateList;
        putcodeGenerateRule(form.value).then((res) => {
          TableListdetailFn();
          proxy?.$modal.msgSuccess('修改成功');
        });
      } else {
        if (postfix) {
          form.value.postfix = postfix;
        }
        form.value.seriationLen = parseInt(form.value.seriationLen);
        form.value.codeGenerateColumnList = data.codeGenerateList;
        codeGenerateRuleAdd(form.value).then((res) => {
          TableListdetailFn();
          proxy?.$modal.msgSuccess('保存成功');
          router.push('/system/codeGenerate');
        });
      }
    }
  });
};
const selectAll = ref(false);
const allclick = () => {
  console.log(1);
  // selectAll.value=!selectAll.value
  // data.codeGenerateList.map(item=>{
  //     console.log(item);
  // })
};
const postfixShow = ref(false);
const stringShow = ref();
const postfixData = ref([]);
let postfix = reactive({
  column: {
    className: '',
    codeGenerateId: '',
    columnComment: '',
    columnConfiguration: '',
    columnId: 0,
    columnType: '',
    createTime: '',
    creator: '',
    elected: 0,
    formatLen: 0,
    formatString: 0,
    id: '',
    javaField: '',
    lastModifiedDate: '',
    lastModifier: '',
    params: {},
    remark: '',
    serialNum: 0
  },
  categoryConfiguration: [],
  value: '',
  type: 0
});
let columnConfiguration = reactive({
  categoryConfiguration: [
    {
      addInChar: '',
      formatLen: 0,
      formatString: 0,
      select: 0,
      serialNum: 0
    }
  ],
  categoryNum: 0,
  columnName: '',
  conditionColumn: '',
  dictType: '',
  tableName: '',
  type: 0
});
const postfixOpen = () => {
  postfixShow.value = true;
  stringShow.value = postfix.type;
  for (let item of data.codeGenerateList) {
    postfixData.value.push({
      dictLabel: item.columnComment,
      dictValue: item.columnComment
    });
  }
};
let isSelects = ref(true);
const isSelect = (val) => {
  isSelects.value = false;
  const selectedData = data.codeGenerateList.find((item) => item.columnComment === val);
  if (selectedData) {
    postfix.column.className = selectedData.className;
    postfix.column.columnId = selectedData.columnId;
    postfix.column.columnType = selectedData.columnType;
    postfix.column.javaField = selectedData.javaField;
  }
};
const postfixHeti = () => {
  form.value.postfix = postfix;
  console.log(form.value.postfix);
  postfixShow.value = false;
};
const peizhiZI = () => {
  if (peizhiShow.value === 0) {
    data.codeGenerateList[columnConfigurationIndex].columnConfiguration = null;
  }
  data.codeGenerateList[columnConfigurationIndex].columnConfiguration = columnConfiguration;
  columnConfigurationShow.value = false;
  console.log(form.value.codeGenerateColumnList);
};
const handleRadioChange = (val) => {
  if (val === 1 || val === 2 || val === 3 || val === 4) {
    stringShow.value = val;
  } else {
    stringShow.value = null;
  }
};
const peizhiShow = ref();
const peizhiRadioChange = (val) => {
  if (val === 1 || val === 2 || val === 3 || val === 0) {
    peizhiShow.value = val;
  } else {
    peizhiShow.value = null;
  }
};

const columnConfigurationShow = ref(false);
let columnConfigurationIndex;
const ziDuan = (row, index) => {
  columnConfigurationIndex = index;
  columnConfigurationShow.value = true;
  if (!row.columnConfiguration) {
    row.columnConfiguration = {
      categoryConfiguration: [
        {
          serialNum: 1,
          formatLen: '',
          formatString: '',
          columnType: ''
        }
      ],
      categoryNum: 0,
      columnName: '',
      conditionColumn: '',
      dictType: '',
      tableName: '',
      type: 0
    };
  }
  columnConfiguration = row.columnConfiguration;
  peizhiShow.value = columnConfiguration.type;
};

const handleChange = (val) => {
  if (val > columnConfiguration.categoryConfiguration.length) {
    let num = val - columnConfiguration.categoryConfiguration.length;
    for (let i = 0; i < num; i++) {
      columnConfiguration.categoryConfiguration.push({});
    }
  } else if (val < columnConfiguration.categoryConfiguration.length) {
    let num = columnConfiguration.categoryConfiguration.length - val;
    columnConfiguration.categoryConfiguration.splice(-num, num);
  }
};
const handleChange2 = (val) => {
  if (val > postfix.categoryConfiguration.length) {
    let num = val - postfix.categoryConfiguration.length;
    for (let i = 0; i < num; i++) {
      postfix.categoryConfiguration.push({});
    }
  } else if (val < postfix.categoryConfiguration.length) {
    let num = postfix.categoryConfiguration.length - val;
    postfix.categoryConfiguration.splice(-num, num);
  }
};
const del = (index) => {
  columnConfiguration.categoryConfiguration.splice(index, 1);
  columnConfiguration.categoryNum = columnConfiguration.categoryConfiguration.length;
};
const del2 = (index) => {
  postfix.categoryConfiguration.splice(index, 1);
  postfix.categoryNum = postfix.categoryConfiguration.length;
};
const checkAll = ref(true);
const isIndeterminate = ref(false);
const handleCheckAllChange = (val) => {
  if (val == true) {
    data.codeGenerateList.map((item) => {
      item.elected = 1;
    });
  } else {
    data.codeGenerateList.map((item) => {
      item.elected = 0;
    });
  }
};
const handleCheckedCitiesChange = (val) => {
  let num = 0;
  data.codeGenerateList.map((item) => {
    if (item.elected == 1) {
      num += 1;
    }
  });
  checkAll.value = num === data.codeGenerateList.length;

  isIndeterminate.value = num > 0 && num < data.codeGenerateList.length;
};
const selectedId = ref();
const handleSelectChange = (value) => {
  selectedId.value = value; // 更新选中的值
  postfix.column = postfixList.value.find((item) => item.id === value); // 更新postfix.column
  console.log(postfix.column);
  postfix.column.columnConfiguration = null;
};
</script>
<style lang="scss" scoped>
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: right;
}

::v-deep(.el-checkbox__inner) {
  transform: scale(1.5) !important;
}
:deep(.el-form-item--default) {
  margin-bottom: 22px;
}
.selectShow {
  margin-top: 10px;
}
.centerSelect {
  margin-top: 10px;
}
.fa-box{
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
    .table-box{
        flex-grow: 1;
        overflow: auto;
    }
}
</style>
