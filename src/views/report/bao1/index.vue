<template>
  <div class="dashboard">
    <!-- Header -->
    <div class="header">
      <div class="bg_header">
        <div class="header_nav fl t_title">xxxxxxxx</div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="data_content">
      <div class="data_main">
        <!-- Left Column -->
        <div class="main_left fl">
          <!-- Chart 1 - 全区快递企业月寄递量 -->
          <div class="left_1 t_btn6" style="cursor: pointer">
            <BorderDecoration />
            <div class="main_title">
              <img src="../images/t_1.png" alt="" />
              xxxxxxx
            </div>
            <Chart1 />
          </div>

          <!-- Chart 2 - 电子商务销售额、订单数 -->
          <div class="left_2" style="cursor: pointer">
            <BorderDecoration />
            <div class="main_title">
              <img src="../images/t_2.png" alt="" />
              xxxxxxx
            </div>
            <Chart2 />
          </div>
        </div>

        <!-- Center Column -->
        <div class="main_center fl">
          <div class="center_text" style="position: relative">
            <BorderDecoration />
            <div class="main_title" style="width: 230px">
              <img src="../images/t_3.png" alt="" />
              xxxxx
            </div>
            <!-- <MapChart /> -->
            <div class="linshi_zdy">
              <!-- 地图图例可以在这里添加 -->
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="main_right fr">
          <!-- Chart 3 - 全区快递月寄递数量 -->
          <div class="right_1">
            <BorderDecoration />
            <div class="main_title" style="width: 220px">
              <img src="../images/t_4.png" alt="" />
              xxxxx
            </div>
            <Chart3 />
          </div>

          <!-- Chart 4 - 电商企业入驻情况 -->
          <div class="right_2">
            <BorderDecoration />
            <div class="main_title" style="width: 200px">
              <img src="../images/t_5.png" alt="" />
              xxxxx
            </div>
            <Chart4 />
          </div>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="data_bottom">
        <!-- Bottom Left - 农村电商交易概况 -->
        <div class="bottom_1 fl t_btn5" style="cursor: pointer">
          <BorderDecoration />
          <div class="main_title">
            <img src="../images/t_7.png" alt="" />
            xxxxx
          </div>
          <div class="main_table two-columns t_btn8">
            <el-table :data="tradeOverview" style="width: 100%" show-overflow-tooltip table-layout="fixed">
              <el-table-column prop="name" label="概况名称" />
              <el-table-column prop="value" label="详情" />
            </el-table>
          </div>
        </div>

        <!-- Bottom Center -->
        <div class="bottom_center fl">
          <!-- 热销产品排行榜 -->
          <div class="bottom_2 fl">
            <BorderDecoration />
            <div class="main_title" style="width: 300px">
              <img src="../images/t_7.png" alt="" />
              xxxxxx
            </div>
            <div class="main_table seven-columns t_btn8">
              <el-table :data="hotProducts" style="width: 100%" show-overflow-tooltip table-layout="fixed">
                <el-table-column prop="name" label="产品名称" />
                <el-table-column prop="category" label="品种" />
                <el-table-column prop="origin" label="产地" />
                <el-table-column prop="price" label="价格" />
                <el-table-column prop="sales" label="销量" />
                <el-table-column prop="rating" label="评分" />
                <el-table-column prop="stock" label="库存状态" />
              </el-table>
            </div>
          </div>

          <!-- 热销店铺排行榜 -->
          <div class="bottom_3 fl">
            <BorderDecoration />
            <div class="main_title" style="width: 260px">
              <img src="../images/t_7.png" alt="" />
              xxxx
            </div>
            <div class="main_table three-columns t_btn2">
              <el-table :data="hotShops" style="width: 100%" show-overflow-tooltip table-layout="fixed">
                <el-table-column prop="name" label="店铺名称" />
                <el-table-column prop="product" label="主营产品" />
                <el-table-column prop="sales" label="销量" />
              </el-table>
            </div>
          </div>
        </div>

        <!-- Bottom Right - 平台活动案例 -->
        <div class="bottom_4 fr">
          <BorderDecoration />
          <div class="main_title">
            <img src="../images/t_7.png" alt="" />
            xxxxx
          </div>
          <div class="main_table three-columns t_btn3 table_zdy">
            <el-table :data="activities" style="width: 100%" show-overflow-tooltip table-layout="fixed">
              <el-table-column prop="theme" label="活动主题">
                <template #default="scope">
                  <a v-if="scope.row.link" :href="scope.row.link" target="_blank">
                    {{ scope.row.theme }}
                  </a>
                  <span v-else>{{ scope.row.theme }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="location" label="活动举办地" />
              <el-table-column prop="date" label="日期" />
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script  setup>
import { ref, onMounted } from 'vue';
import BorderDecoration from './BorderDecoration.vue';
import Chart1 from './charts/Chart1.vue';
import Chart2 from './charts/Chart2.vue';
import Chart3 from './charts/Chart3.vue';
import Chart4 from './charts/Chart4.vue';
import MapChart from './charts/MapChart.vue';

const tableData = [
  {
    date: '2016-05-03',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles'
  }
];
const tradeOverview = ref([
  { name: '累计交易总金额', value: '4058.56 万元' },
  { name: '累计交易订单数量', value: '437753 件' },
  { name: '累计产品SKU数量', value: '360 个' },
  { name: '本月交易总额', value: '242.42 万元' },
  { name: '本月交易订单数量', value: '5283 件' }
]);

// 热销产品数据 - 增加更多列和行用于测试滚动
const hotProducts = ref([
  { name: '荔浦百香果', category: '百香果', origin: '荔浦', price: '25.8元/斤', sales: '2.8万', rating: '4.9分', stock: '充足' },
  { name: '荔浦砂糖桔', category: '砂糖桔', origin: '荔浦', price: '18.5元/斤', sales: '3.2万', rating: '4.8分', stock: '充足' },
  { name: '90g将军峰小方盒绿茶', category: '中小叶种', origin: '广西贺州', price: '128元/盒', sales: '1.5万', rating: '4.7分', stock: '紧张' },
  { name: '珍珠糯玉米', category: '粮食', origin: '忻城县', price: '12.8元/斤', sales: '2.1万', rating: '4.6分', stock: '充足' },
  { name: '桂花红糖', category: '桂花红糖', origin: '大新县', price: '35.6元/盒', sales: '1.8万', rating: '4.9分', stock: '一般' },
  { name: '广西芒果', category: '热带水果', origin: '百色市', price: '22.8元/斤', sales: '4.2万', rating: '4.8分', stock: '充足' },
  { name: '桂林米粉', category: '特色小食', origin: '桂林市', price: '15.8元/包', sales: '3.8万', rating: '4.7分', stock: '充足' },
  { name: '柳州螺蛳粉', category: '特色小食', origin: '柳州市', price: '19.9元/包', sales: '5.6万', rating: '4.9分', stock: '充足' },
  { name: '南宁老友粉', category: '特色小食', origin: '南宁市', price: '16.8元/包', sales: '2.9万', rating: '4.6分', stock: '一般' },
  { name: '梧州龟苓膏', category: '传统甜品', origin: '梧州市', price: '28.8元/盒', sales: '1.9万', rating: '4.8分', stock: '充足' }
]);

// 热销店铺数据
const hotShops = ref([
  { name: '鲜迪食品专营店', product: '海鸭蛋', sales: '2.8万' },
  { name: '中鼎水果专营店', product: '红心芭乐番石榴', sales: '2.5万' },
  { name: '中闽飘香旗舰店', product: '广西桂林罗汉果', sales: '2.4万' },
  { name: '芋小妹旗舰店', product: '广西荔浦大芋头', sales: '1.3万' },
  { name: '桂甄堂旗舰店', product: '柳州螺狮粉', sales: '1.1万' }
]);

// 平台活动案例数据
const activities = ref([
  {
    theme: '2018广西特产行销全国',
    location: '南宁',
    date: '2018年',
    link: 'http://www.gxitps.org/zhanhui/detail/id/20.html'
  },
  {
    theme: '2018壮族三月三电商节',
    location: '南宁',
    date: '2018年',
    link: 'http://www.gxitps.org/zhanhui/detail/id/16.html'
  },
  {
    theme: '2018灵山荔枝节',
    location: '灵山县',
    date: '2018年',
    link: 'http://www.gxitps.org/zhanhui/detail/id/17.html'
  },
  { theme: '2018年货节', location: '广西', date: '2018年' },
  { theme: '2017好讲师大赛', location: '南宁', date: '2017年' }
]);
const gridOptions = reactive({
  // border: true,
  border: 'none',
  headerCellClassName({ column }) {
    if (column.field === 'name') {
      return 'touming';
    }
    return 'touming';
  },
  rowClassName({ rowIndex }) {
    return 'touming';
  },
  columns: [
    { field: 'name', title: 'Name', width: 100 },
    { field: 'sex', title: 'Sex', width: 100 },
    { field: 'age', title: 'Age', width: 100 },
    { field: 'attr1', title: 'Attr1', width: 100 },
    { field: 'address', title: 'Address', width: 100 }
  ],
  data: [
    { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
    { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
    { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
    { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 23, address: 'test abc' },
    { id: 10005, name: 'Test5', role: 'Develop', sex: 'Women', age: 30, address: 'Shanghai' },
    { id: 10006, name: 'Test6', role: 'Designer', sex: 'Women', age: 21, address: 'test abc' },
    { id: 10007, name: 'Test7', role: 'Test', sex: 'Man', age: 29, address: 'test abc' },
    { id: 10008, name: 'Test8', role: 'Develop', sex: 'Man', age: 35, address: 'test abc' }
  ]
});
</script>
<style lang='scss' scoped>
* {
  margin: 0;
  padding: 0;
  font-family: PingFangSC-Light, 微软雅黑;
}

body,
html {
  width: 100%;
  height: auto;
  color: #333;
  background: url('../images/bg.jpg') no-repeat;
  background-size: 100% 100%;
}

/* 清除浮动 */
.clear-both:before,
.clear-both:after {
  display: table;
  content: '';
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  clear: both;
}

.clearfix {
  *zoom: 1; /*IE/7/6*/
}

.fl {
  float: left;
}

.fr {
  float: right;
}

/* 去除默认样式 */
fieldset,
img,
input,
button {
  border: none;
  padding: 0;
  margin: 0;
  outline-style: none;
}

img {
  border: 0;
  vertical-align: middle;
}

ul,
li {
  list-style: none;
}

a {
  text-decoration: none;
  cursor: pointer;
}

/* 标题样式 */
.t_title {
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 2.5em;
  line-height: 80px;
  color: #fff;
}

/* 图表容器样式 */
#chart_map {
  cursor: pointer;
}

.t_show {
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 2px;
  background: #2c58a6;
  padding: 2px 5px;
  color: #fff;
  cursor: pointer;
}

/* 边框装饰样式 */
.t_line_box {
  position: absolute;
  width: 20px;
  height: 20px;
}

.t_line_box:nth-child(1) {
  top: 0;
  left: 0;
}

.t_line_box:nth-child(2) {
  top: 0;
  right: 0;
}

.t_line_box:nth-child(3) {
  bottom: 0;
  left: 0;
}

.t_line_box:nth-child(4) {
  bottom: 0;
  right: 0;
}

/* 边框线条 */
.t_l_line,
.l_t_line,
.t_r_line,
.r_t_line,
.l_b_line,
.b_l_line,
.r_b_line,
.b_r_line {
  position: absolute;
  background: #4b8df8;
}

.t_l_line,
.t_r_line {
  width: 20px;
  height: 2px;
  top: 0;
}

.l_t_line,
.r_t_line {
  width: 2px;
  height: 20px;
  left: 0;
}

.r_t_line {
  right: 0;
  left: auto;
}

.l_b_line,
.r_b_line {
  width: 2px;
  height: 20px;
  bottom: 0;
}

.r_b_line {
  right: 0;
}

.b_l_line,
.b_r_line {
  width: 20px;
  height: 2px;
  bottom: 0;
}

.b_r_line {
  right: 0;
}
::v-deep(.mygrid-style.vxe-grid .vxe-body--row.row-green) {
  background-color: #187;
  color: #fff;
}
::v-deep(.mygrid-style.vxe-grid .vxe-header--column.col-blue) {
  background-color: #2db7f5;
  color: #fff;
}
::v-deep(.mygrid-style.vxe-grid .vxe-body--column.col-red) {
  background-color: red;
  color: #fff;
}
::v-deep(.mygrid-style.vxe-grid .vxe-header--column.touming) {
  // background: rgba(44, 88, 166, 0.8);
  background-color: #2c58a6;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
::v-deep(.mygrid-style.vxe-grid .vxe-body--row.touming) {
  background-color: transparent;
  color: black;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.dashboard {
  width: 100vw;
  height: 100vh;
  background: url('../images/bg.jpg') no-repeat;
  background-size: 100% 100%;
  overflow: hidden; /* 绝对不允许滚动条 */
  display: flex;
  flex-direction: column;
}

/* Header样式 */
.header {
  width: 100%;
  height: 6vh; /* 减少头部高度 */
  flex-shrink: 0; /* 防止头部被压缩 */
}

.bg_header {
  width: 100%;
  height: 100%;
  background: url('../images/title.png') no-repeat;
  background-size: 100% 100%;
}

/* Dashboard样式 - 自适应布局 */
.data_content {
  flex: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  padding: 0.5vh 1vw; /* 减少padding */
  box-sizing: border-box;
  overflow: hidden; /* 确保不出现滚动条 */
}

/* Main layout */
.data_main {
  flex: 1; /* 占据可用空间 */
  display: flex;
  align-items: stretch;
  gap: 1vw; /* 使用视口宽度单位作为间距 */
  margin-bottom: 0.5vh; /* 减少底部间距 */
}

.main_left {
  flex: 0 0 30%; /* 固定30% */
  display: flex;
  flex-direction: column;
  gap: 1vh; /* 内部元素间距 */
}

.main_center {
  flex: 1; /* 占满剩余空间 */
  display: flex;
  flex-direction: column;
}

.main_right {
  flex: 0 0 30%; /* 固定30% */
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

/* Chart containers */
.left_1,
.left_2,
.right_1,
.right_2,
.center_text {
  position: relative;
  background: rgba(6, 30, 93, 0.5);
  border: 1px solid rgba(25, 186, 139, 0.17);
  padding: 3vh 1vw 1vh 1vw; /* 减少padding */
  box-sizing: border-box;
  overflow: hidden;
}

/* 左右列的容器各占一半高度 */
.left_1,
.left_2,
.right_1,
.right_2 {
  flex: 1; /* 平分可用空间 */
}

/* 中间容器填充整个列高度 */
.center_text {
  flex: 1;
}

.main_title {
  width: min(245px, 80%); /* 响应式宽度 */
  height: 3.5vh; /* 使用视口高度 */
  line-height: 3.3vh;
  background-color: #2c58a6;
  border-radius: 18px;
  position: absolute;
  top: 0.2vh; /* 调整位置适应新的padding */
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: clamp(14px, 1.2vw, 18px); /* 响应式字体 */
  font-weight: 600;
  box-sizing: border-box;
  padding-left: 3vw;
  z-index: 999999;
  text-align: center;
}

.main_title img {
  position: absolute;
  top: 0.8vh;
  left: 1.5vw;
  width: auto;
  height: 60%; /* 相对于标题高度 */
}

/* Bottom section */
.data_bottom {
  flex: 0 0 auto; /* 不拉伸，保持内容高度 */
  display: flex;
  gap: 1vw;
  height: 26vh; /* 进一步减少底部区域高度 */
}

.bottom_1 {
  flex: 0 0 25%;
}

.bottom_center {
  flex: 0 0 48%;
  display: flex;
  gap: 1vw;
}

.bottom_2,
.bottom_3 {
  flex: 1; /* 平分 bottom_center 的空间 */
}

.bottom_4 {
  flex: 0 0 25%;
}

.bottom_1,
.bottom_2,
.bottom_3,
.bottom_4 {
  position: relative;
  background: rgba(6, 30, 93, 0.5);
  border: 1px solid rgba(25, 186, 139, 0.17);
  padding: 3vh 1vw 0 1vw; /* 减少padding */
  box-sizing: border-box;
  overflow: hidden;
}

/* Table styles - 完美边框对齐的表格布局 */
.main_table {
  margin-top: 1vh;
  height: calc(100% - 2vh); /* 占满剩余高度 */
  width: 100%; /* 严格限制宽度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整个容器滚动 */
  // border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
}

.main_table table {
  width: 100%;
  border-collapse: collapse; /* 使用collapse确保边框合并 */
  color: #fff;
  font-size: clamp(10px, 0.8vw, 14px);
  height: 100%;
  display: block; /* 改为block布局 */
}

/* 表头样式 - 固定不滚动 */
.main_table thead {
  display: block;
  width: 100%;
  position: relative;
  z-index: 10;
}

.main_table thead tr {
  display: flex; /* 使用flex确保精确控制 */
  width: 100%;
  background: rgba(44, 88, 166, 0.9);
}

.main_table th {
  flex: 1; /* 平分宽度 */
  padding: 0.8vh 0.5vw;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top: none; /* 顶部边框由容器提供 */
  background: rgba(44, 88, 166, 0.9);
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  position: relative;
}

.main_table th:first-child {
  border-left: none; /* 左边框由容器提供 */
}

.main_table th:last-child {
  border-right: none; /* 右边框由容器提供 */
}

/* 表体样式 - 可滚动，边框完美对齐 */
.main_table tbody {
  display: block;
  overflow: auto; /* 支持纵向和横向滚动 */
  width: 100%;
  flex: 1; /* 占满剩余空间 */
}

.main_table tbody tr {
  display: flex; /* 与表头保持一致的flex布局 */
  width: 100%;
}

.main_table td {
  flex: 1; /* 与表头th保持一致的flex: 1 */
  padding: 0.6vh 0.5vw;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top: none; /* 避免重复边框 */
  background: rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  position: relative;
}

.main_table td:first-child {
  border-left: none; /* 左边框由容器提供 */
}

.main_table td:last-child {
  border-right: none; /* 右边框由容器提供 */
}

/* 最后一行去掉底边框，避免与容器边框重复 */
.main_table tbody tr:last-child td {
  border-bottom: none;
}

/* 鼠标悬停效果 */
.main_table tbody tr:hover td {
  background: rgba(44, 88, 166, 0.3);
}

/* 自定义滚动条样式 */
.main_table tbody::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.main_table tbody::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.main_table tbody::-webkit-scrollbar-thumb {
  background: rgba(44, 88, 166, 0.6);
  border-radius: 4px;
}

.main_table tbody::-webkit-scrollbar-thumb:hover {
  background: rgba(44, 88, 166, 0.8);
}

.main_table tbody::-webkit-scrollbar-corner {
  background: rgba(255, 255, 255, 0.1);
}

/* 针对不同列数的表格，精确设置列宽 */
.main_table.two-columns th,
.main_table.two-columns td {
  flex: 0 0 50%; /* 精确的50%宽度 */
  max-width: 50%;
}

.main_table.three-columns th,
.main_table.three-columns td {
  flex: 0 0 33.333333%; /* 精确的1/3宽度 */
  max-width: 33.333333%;
}

.main_table.seven-columns th,
.main_table.seven-columns td {
  flex: 0 0 14.285714%; /* 精确的1/7宽度 */
  max-width: 14.285714%;
}

.main_table a {
  color: #4bf0ff;
  text-decoration: none;
}

.main_table a:hover {
  text-decoration: underline;
}

/* 地图图例样式 */
.linshi_zdy {
  position: absolute;
  right: 1vw;
  bottom: 5vh; /* 使用底部定位 */
}

.linshi_zdy li {
  width: 10vw;
  font-size: clamp(12px, 1vw, 16px);
  padding: 0.3vh 0.8vw;
  cursor: pointer;
}

.linshi_zdy span {
  display: block;
  width: 1vw;
  height: 1vw;
  float: left;
  border-radius: 50%;
  margin-top: 0.3vh;
  margin-right: 0.5vw;
}

.linshi_zdy li:first-child {
  color: #ff0000;
}

.linshi_zdy li:first-child span {
  background: #ff0000;
}

.linshi_zdy li:nth-child(2) {
  color: #9cff00;
}

.linshi_zdy li:nth-child(2) span {
  background: #9cff00;
}

.linshi_zdy li:nth-child(3) {
  color: #fff;
}

.linshi_zdy li:nth-child(3) span {
  background: #fff;
}

.linshi_zdy li:nth-child(4) {
  color: #f4a100;
}

.linshi_zdy li:nth-child(4) span {
  background: #f4a100;
}
/* el-table 样式优化 - 严格控制宽度 */
::v-deep .el-table {
  background-color: transparent;
  width: 100% !important; /* 强制宽度为100% */
  table-layout: fixed !important; /* 强制固定布局 */
  max-width: 100% !important; /* 防止超出容器 */
  overflow: hidden !important; /* 防止溢出 */
}

/* 表头样式 */
::v-deep .el-table__header {
  background-color: transparent;
  width: 100% !important;
}

::v-deep .el-table__header-wrapper {
  width: 100% !important;
  overflow: hidden !important;
}

::v-deep .el-table__header-wrapper th {
  color: white;
  background-color: #2b58a6 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  box-sizing: border-box !important;
}

/* 表体样式 */
::v-deep .el-table__body {
  background-color: transparent;
  width: 100% !important;
}

::v-deep .el-table__body-wrapper {
  width: 100% !important;
  overflow: auto !important; /* 允许滚动 */
  max-height: calc(100% - 40px) !important; /* 减去表头高度 */
}

::v-deep .el-table__body tr > td {
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  box-sizing: border-box !important;
}

/* 表格行样式 */
::v-deep .el-table__row {
  background-color: transparent;
  width: 100% !important;
}

/* 悬停效果 */
::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: rgba(15, 166, 255, 0.26) !important;
}

/* 确保所有列平分宽度 */
::v-deep .el-table .el-table__cell {
  padding: 8px 4px !important; /* 减少内边距 */
  box-sizing: border-box !important;
}

/* 针对不同容器的表格宽度控制 */
.main_table ::v-deep .el-table {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.main_table ::v-deep .el-table__header-wrapper {
  flex-shrink: 0 !important;
}

.main_table ::v-deep .el-table__body-wrapper {
  flex: 1 !important;
  overflow: auto !important;
}

/* 确保列宽平分 */
.main_table.two-columns ::v-deep .el-table__header-wrapper th,
.main_table.two-columns ::v-deep .el-table__body td {
  width: 50% !important;
  max-width: 50% !important;
}

.main_table.three-columns ::v-deep .el-table__header-wrapper th,
.main_table.three-columns ::v-deep .el-table__body td {
  width: 33.333333% !important;
  max-width: 33.333333% !important;
}

.main_table.seven-columns ::v-deep .el-table__header-wrapper th,
.main_table.seven-columns ::v-deep .el-table__body td {
  width: 14.285714% !important;
  max-width: 14.285714% !important;
}
</style>
