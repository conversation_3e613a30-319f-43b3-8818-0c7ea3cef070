<template>
  <div class="app-container home">
    <div class="demo-buttons">
      <h3>选择不同的动画效果：</h3>
      <div class="button-grid">
        <el-button type="primary" @click="openModal('fade-scale')">淡入放大</el-button>
        <el-button type="success" @click="openModal('slide-down')">从上滑入</el-button>
        <el-button type="info" @click="openModal('slide-up')">从下滑入</el-button>
        <el-button type="warning" @click="openModal('slide-left')">从左滑入</el-button>
        <el-button type="danger" @click="openModal('slide-right')">从右滑入</el-button>
        <el-button type="primary" plain @click="openModal('rotate')">旋转进入</el-button>
        <el-button type="success" plain @click="openModal('flip')">翻转进入</el-button>
        <el-button type="info" plain @click="openModal('bounce')">弹跳进入</el-button>
      </div>

      <div class="size-buttons">
        <h4>不同尺寸：</h4>
        <el-button @click="openModalWithSize('small')">小弹窗</el-button>
        <el-button @click="openModalWithSize('medium')">中等弹窗</el-button>
        <el-button @click="openModalWithSize('large')">大弹窗</el-button>
        <el-button @click="openFullscreenModal">全屏弹窗</el-button>
      </div>
    </div>

    <CustomModal
      v-model:visible="dialogVisible"
      :title="modalTitle"
      :animation="currentAnimation"
      :size="modalSize"
      :fullscreen="isFullscreen"
      :show-close="true"
      :close-on-click-outside="true"
      :close-on-escape="true"
      @close="handleModalClose"
      @open="handleModalOpen"
    >
      <div class="modal-content">
        <h2>{{ modalTitle }}</h2>
        <p>这是一个自定义弹窗，具有更好的动画效果！</p>
        <p>
          当前动画类型：<strong>{{ currentAnimation }}</strong>
        </p>
        <p>
          当前尺寸：<strong>{{ modalSize }}</strong>
        </p>

        <div class="content-demo">
          <el-form :model="form" label-width="100px">
            <el-form-item label="用户名">
              <el-input v-model="form.username" placeholder="请输入用户名" />
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="form.email" placeholder="请输入邮箱" />
            </el-form-item>
            <el-form-item label="描述">
              <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述" />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <el-button @click="closeModal">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </template>
    </CustomModal>
  </div>
</template>

<script setup name="Index" lang="ts">
import { ref } from 'vue';

const dialogVisible = ref(false);
const currentAnimation = ref<'fade-scale' | 'slide-down' | 'slide-up' | 'slide-left' | 'slide-right' | 'rotate' | 'flip' | 'bounce'>('fade-scale');
const modalSize = ref<'small' | 'medium' | 'large'>('medium');
const isFullscreen = ref(false);
const modalTitle = ref('自定义弹窗');

// 表单数据
const form = ref({
  username: '',
  email: '',
  description: ''
});

// 打开弹窗 - 指定动画
const openModal = (animation: typeof currentAnimation.value) => {
  currentAnimation.value = animation;
  modalSize.value = 'medium';
  isFullscreen.value = false;
  modalTitle.value = `${getAnimationName(animation)} 弹窗`;
  dialogVisible.value = true;
};

// 打开弹窗 - 指定尺寸
const openModalWithSize = (size: typeof modalSize.value) => {
  modalSize.value = size;
  currentAnimation.value = 'fade-scale';
  isFullscreen.value = false;
  modalTitle.value = `${getSizeName(size)} 弹窗`;
  dialogVisible.value = true;
};

// 打开全屏弹窗
const openFullscreenModal = () => {
  isFullscreen.value = true;
  currentAnimation.value = 'slide-down';
  modalTitle.value = '全屏弹窗';
  dialogVisible.value = true;
};

// 关闭弹窗
const closeModal = () => {
  dialogVisible.value = false;
};

// 确认操作
const handleConfirm = () => {
  console.log('表单数据:', form.value);
  // 这里可以处理表单提交逻辑
  closeModal();
};

// 弹窗关闭事件
const handleModalClose = () => {
  console.log('弹窗已关闭');
  // 重置表单
  form.value = {
    username: '',
    email: '',
    description: ''
  };
};

// 弹窗打开事件
const handleModalOpen = () => {
  console.log('弹窗已打开');
};

// 获取动画名称
const getAnimationName = (animation: string) => {
  const names: Record<string, string> = {
    'fade-scale': '淡入放大',
    'slide-down': '从上滑入',
    'slide-up': '从下滑入',
    'slide-left': '从左滑入',
    'slide-right': '从右滑入',
    'rotate': '旋转进入',
    'flip': '翻转进入',
    'bounce': '弹跳进入'
  };
  return names[animation] || animation;
};

// 获取尺寸名称
const getSizeName = (size: string) => {
  const names: Record<string, string> = {
    'small': '小',
    'medium': '中等',
    'large': '大'
  };
  return names[size] || size;
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.demo-buttons {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-buttons h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 18px;
}

.demo-buttons h4 {
  margin: 20px 0 10px;
  color: #666;
  font-size: 16px;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 30px;
}

.size-buttons {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.size-buttons .el-button {
  margin-right: 12px;
  margin-bottom: 8px;
}

.modal-content {
  h2 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #333;
    font-size: 24px;
  }

  p {
    margin-bottom: 12px;
    color: #666;
    line-height: 1.6;
  }
}

.content-demo {
  margin-top: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}
</style>