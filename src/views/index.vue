<template>
  <div class="app-container home">
    <el-button type="primary" @click="openFullscreenDialog">打开全屏弹窗</el-button>
    
    <el-dialog
      v-model="dialogVisible"
      fullscreen
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :destroy-on-close="true"
      custom-class="fullscreen-dialog"
    >
      <div class="dialog-content">
        <h2>全屏弹窗内容</h2>
        <p>这里是弹窗的内容区域</p>
        
        <el-button
          class="close-btn"
          type="danger"
          @click="closeDialog"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Index" lang="ts">
import { ref } from 'vue';

const dialogVisible = ref(false);

const openFullscreenDialog = () => {
  dialogVisible.value = true;
};

const closeDialog = () => {
  dialogVisible.value = false;
};
</script>

<style lang="scss" scoped>
.fullscreen-dialog {
  animation: dialog-fade-in 0.4s;
}

.dialog-content {
  position: relative;
  height: 100%;
  padding: 20px;
}

.close-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
}

@keyframes dialog-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

:deep(.el-dialog) {
  margin: 0 !important;
}
</style>