import { reactive } from 'vue';
export function index(dictTypes) {
  const inputFilterRender = reactive({
    name: 'VxeInput'
  });

  const ProcessingData = reactive({
    id: 'departmentOrderData',
    height: '100%',
    emptyText: '没有更多数据了！',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: false
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    rowConfig: {
      isCurrent: true,
      isHover: true
    },
    loading: false,
    columns: [
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '150',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '款式编号',
        field: 'styleCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '派工编号',
        field: 'dispatchOrderNumber',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '线别',
        field: 'deptName',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      }
    ],
    data: []
  });
  const ProcessingDataSizeData = reactive({
    id: 'departmentOrderData',
    height: '100%',
    emptyText: '没有更多数据了！',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: false
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [{ type: 'seq', field: '', width: 70, align: 'center' }],
    data: []
  });
  return { ProcessingData, ProcessingDataSizeData };
}
