<template>
  <div class="fa_box">
    <div class="grid grid-cols-1 h-full fa-content" style="display: flex; flex-direction: column">
      <div class="bg-white rounded-lg shadow-md p-2" style="flex: 0.68; min-height: 0">
        <div class="flex h-full" style="min-height: 0; width: 100%" id="topboxID">
          <div class="left-Information" style="flex: 0.45; margin-right: 1rem">
            <div class="h-full" style="display: flex">
              <fieldset
                class="foundation"
                style="
                  flex: 0.6;
                  margin-right: 1rem;
                  background: white;
                  border-radius: 8px;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                  padding: 10px;
                  overflow: auto;
                "
              >
                <legend>信息</legend>
                <div class="grid grid-cols-1 gap-4">
                  <div class="info-item">
                    <span>品牌:{{ activeLeftForm.customName }}</span>
                  </div>
                  <div class="info-item">
                    <span>生产开始时间:{{ activeLeftForm?.createTime ? activeLeftForm.createTime.split(' ')[0] : '' }}</span>
                  </div>
                  <div class="info-item">
                    <span>加工上线日期:{{ activeLeftForm?.startTime ? activeLeftForm.startTime.split(' ')[0] : '' }}</span>
                  </div>
                  <div class="info-item">
                    <span>生产线:{{ activeLeftForm.deptName }}</span>
                  </div>
                  <div class="info-item">
                    <span>Po数量:{{ activeLeftForm.totalCount }}</span>
                  </div>
                  <div class="info-item">
                    <span>CSD:{{ activeLeftForm?.shipmentDate ? activeLeftForm.shipmentDate.split(' ')[0] : '' }}</span>
                  </div>
                </div>
              </fieldset>
              <fieldset
                class="img-box"
                style="flex: 0.4; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); padding: 16px; overflow: auto"
              >
                <legend>详情</legend>
                <div style="display: flex; justify-content: center; align-items: center; height: 100px">
                  <!-- <img
                    v-if="activeLeftForm.styleImageBase64"
                    :src="activeLeftForm.styleImageBase64"
                    alt="产品图片"
                    style="height: 100px; width: 100px; object-fit: contain"
                  /> -->
                  <!-- <el-image style="width: 100px; height: 100px" :src="activeLeftForm.styleImageBase64" fit="contain" /> -->
                  <el-image
                    style="width: 100px; height: 100px"
                    :src="activeLeftForm.styleImageBase64"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="[activeLeftForm.styleImageBase64]"
                    show-progress
                    :initial-index="4"
                    fit="contain"
                  />
                </div>
                <div class="text-center">
                  <p style="font-size: 14px">{{ activeLeftForm.customStyleCode }}</p>
                  <el-button type="success" class="!rounded-full w-70px h-70px" style="width: 100px; height: 100px; padding: 0" @click="goCheck">
                    进入检查
                  </el-button>
                </div>
              </fieldset>
            </div>
          </div>
          <div class="right-Information" style="flex: 0.55">
            <fieldset
              class="foundation"
              style="flex: 1; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); padding: 10px; overflow: auto"
            >
              <legend>
                订单
                <el-button :icon="Setting" circle @click="isShowdepartmentOrderDataToolbarConfig" />
                <el-input v-model="keyword" style="width: 200px" placeholder="输入关键字" class="input-with-select" clearable>
                  <template #append>
                    <el-button :icon="Search" @click="getTaskAssignProcessListByKeyWordFN" />
                  </template>
                </el-input>
              </legend>
              <div class="flex flex-col h-full" :style="{ width: departmentOrderDataMaxWidth + 'px' }" style="height: 100%">
                <vxe-grid class="mylist-table-left" ref="departmentOrderDataRef" v-bind="ProcessingData" @cell-click="orderDataRowDblclick">
                </vxe-grid>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-md p-2" style="flex: 0.39; min-height: 0">
        <div class="flex h-full" style="min-height: 0; width: 100%" id="bottomboxID">
          <div class="right-Information" style="flex: 1">
            <fieldset
              class="foundation"
              style="flex: 1; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); padding: 10px; overflow: auto"
            >
              <legend>信息</legend>
              <div :style="{ width: sizeDataMaxWidth + 'px' }" style="height: 100%">
                <vxe-grid class="mylist-table-left" ref="departmentSizeDataRef" v-bind="ProcessingDataSizeData">
                  <template #default_completion="{ row }">
                    <el-progress :percentage="(row.completion * 100).toFixed(3)" />
                  </template>
                </vxe-grid>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </div>

    <el-dialog v-model="checkDialogVisible" :fullscreen="true" class="check-dialog">
      <!-- <span style="height: 100%"> -->
      <div class="bg-gray-50">
        <div class="container mx-auto px-6 py-8">
          <!-- 主要内容区域 -->
          <div class="grid grid-cols-12 gap-6">
            <div class="col-span-8 bg-white shadow-sm p-6" style="height: 100%; overflow-y: auto">
              <!-- 缺陷类型选择 -->
              <div class="mb-8">
                <h2 class="text-xl font-medium mb-4">十大不良缺陷</h2>
                <div class="grid grid-cols-3 gap-4">
                  <div
                    v-for="(defect, index) in defectTypes"
                    :key="index"
                    class="defect-card cursor-pointer p-4 rounded-lg border transition-all duration-200 border-gray-200"
                    @click="
                      clickTenDefect(defect);
                      gradeType = 'D';
                      defectTitle = defect.dictValue;
                    "
                  >
                    <div class="text-sm text-gray-600">
                      {{ defect.dictValue }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- JHV编号 -->
              <div class="flex justify-end mb-8">
                <span class="text-gray-600">制令号: </span>
                <span class="ml-2 font-medium">{{ activeLeftForm.customStyleCode }}</span>
              </div>
              <!-- 等级选择 -->
              <div class="mb-8">
                <div class="grid grid-cols-3 gap-4">
                  <div
                    class="defect-card cursor-pointer p-4 rounded-lg border transition-all duration-200 border-gray-200"
                    @click="
                      clickTenDefect(defect);
                      gradeType = 'B';
                      defectTitle = 'B Grade';
                    "
                  >
                    <div class="text-sm text-gray-600">B Grade</div>
                  </div>
                  <div
                    class="defect-card cursor-pointer p-4 rounded-lg border transition-all duration-200 border-gray-200"
                    @click="
                      clickTenDefect(defect);
                      gradeType = 'C';
                      defectTitle = 'C Grade';
                    "
                  >
                    <div class="text-sm text-gray-600">C Grade</div>
                  </div>
                  <div
                    class="defect-card cursor-pointer p-4 rounded-lg border transition-all duration-200 border-gray-200"
                    @click="
                      OTHERDEFECTSIsShow = true;
                      gradeType = 'OTHER';
                    "
                  >
                    <div class="text-sm text-gray-600">其他</div>
                  </div>
                </div>
              </div>
              <!-- 操作按钮 -->
              <div class="flex justify-center items-center space-x-30 items-center ">
                <el-button
                  type="success"
                  class="!rounded-full w-70px h-70px"
                  style="width: 100px; height: 100px; padding: 0"
                  @click="
                    clickTenDefect(defect);
                    gradeType = 'A';
                    defectTitle = '通过';
                  "
                >
                  <el-icon><Check /></el-icon>通过
                </el-button>
                <el-button
                  class="!rounded-button whitespace-nowrap"
                  @click="handleClose"
                  style="width: 100px; height: 100px; padding: 0"
                  type="info"
                  plain
                >
                  <el-icon class="mr-2"><Close /></el-icon>关闭
                </el-button>
              </div>
            </div>
            <!-- 右侧表格区域 -->
            <div class="col-span-4 flex flex-col gap-2.5" style="height: 100%; overflow-y: auto">
              <!-- 第一个表格 (占 65%) -->
              <div class="bg-white rounded-lg shadow-sm p-6 table-container" style="flex: 0.6; display: flex; flex-direction: column">
                <h2 class="text-xl font-medium mb-4">缺陷统计</h2>
                <el-table :data="defectStatistics" stripe style="width: 100%; flex: 1" border :scrollbar-always-on="true">
                  <el-table-column prop="defectName" label="Defect" width="100" />
                  <el-table-column prop="orderStyleSizeName" label="Size" width="80" />
                  <el-table-column prop="totalCount" label="totalCount" />
                </el-table>
              </div>
              <!-- 第二个表格 (占 35%) -->
              <div class="bg-white rounded-lg shadow-sm p-6 table-container" style="flex: 0.4; display: flex; flex-direction: column">
                <h2 class="text-xl font-medium mb-4">前三不良缺陷</h2>
                <el-table :data="TopThreeDefects" stripe style="width: 100%; flex: 1" border :scrollbar-always-on="true">
                  <el-table-column prop="defectName" label="Defect" width="120" />
                  <el-table-column prop="totalCount" label="totalCount" />
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- </span> -->
    </el-dialog>
    <el-dialog v-model="BondingGapIsShow" :fullscreen="true" :title="defectTitle" class="check-dialog2">
      <!-- <span> -->
      <div class="mb-2" style="height: 98%; position: relative">
        <div class="grid grid-cols-3 gap-4">
          <div
            v-for="(defect, index) in BondingGapArr"
            :key="index"
            :class="[
              'defect-card',
              'cursor-pointer',
              'p-4',
              'rounded-lg',
              'border',
              'transition-all',
              'duration-200',
              'border-gray-200',
              { 'disabled': defect.count <= defect.inspired }
            ]"
            @click="clickBondingGap(defect)"
          >
            <div class="text-sm text-gray-600">
              {{ defect.sizeName }}
            </div>
          </div>
        </div>
        <div class="bujiaoBox" style="position: absolute; bottom: 0; right: 0">
          <el-button
            type="primary"
            @click="
              (bujiaoIsShow = true), (selectedIndexes = []);
              selectedDefect = [];
              passWord = '';
            "
            >补交条码</el-button
          >
        </div>
      </div>

      <!-- </span> -->
    </el-dialog>
    <el-dialog v-model="OTHERDEFECTSIsShow" :fullscreen="true" title="OTHER DEFECTS" class="nested-dialog">
      <div class="grid grid-cols-3 gap-4">
        <div
          v-for="(defect, index) in otherDefects"
          :key="index"
          class="defect-card cursor-pointer p-4 rounded-lg border transition-all duration-200 border-gray-200"
          @click="
            clickTenDefect(defect);
            defectTitle = defect.dictValue;
          "
        >
          <div class="text-sm text-gray-600">
            {{ defect.dictValue }}
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-model="bujiaoIsShow" title="补交条码" width="80%">
      <div>
        <div class="grid grid-cols-4 gap-4">
          <div
            v-for="(defect, index) in BondingGapArr"
            :key="index"
            :class="[
              'defect-card',
              'cursor-pointer',
              'p-4',
              'rounded-lg',
              'border',
              'transition-all',
              'duration-200',
              selectedIndexes.includes(index) ? 'border-[#409EFF]' : 'border-gray-200'
            ]"
            style="position: relative"
            @click="handleSelect(index, defect)"
          >
            <div class="text-sm text-gray-600" style="display: flex; align-items: center; justify-content: space-between">
              <span> {{ defect.sizeName }}</span>
              <el-input-number
                v-if="selectedIndexes.includes(index)"
                v-model="defect.quantity"
                style="margin-right: 15px"
                controls-position="right"
                @click.stop
              />
            </div>

            <div v-if="selectedIndexes.includes(index)" style="position: absolute; right: 5px; bottom: 5px">  
              <el-icon color="#409EFF"><Select /></el-icon>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="bujiaoIsShow = false">取消</el-button>
          <el-button type="primary" @click="bujiaoPassWordIsShow = true"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="bujiaoPassWordIsShow" title="提交" width="80%">
      <div>
        <el-input v-model="passWord" style="width: 240px" type="password" placeholder="Please password" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="bujiaoPassWordIsShow = false">取消</el-button>
          <el-button type="primary" @click="shougongdanSubmit"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script  setup>
import { Search, Setting } from '@element-plus/icons-vue';
import { ref } from 'vue';
import {
  getTaskAssignSewingListByKeyWord,
  //   getSizeDetailListByDispatchOrderNumber,
  getSysSewingUndesirableReason,
  getSewingDefectDetailList,
  getTopSewingDefectDetail,
  addSewingInspection,
  getVerticalSizeDetailListByDispatchOrderNumber
} from '@/api/mes/department';
import {
  getTaskAssignProcessListByKeyWord,
  getProcessVerticalSizeDetailListByDispatchOrderNumber,
  getProcessDefectDetailList,
  getTopProcessDefectDetai,
  getSizeDetailListByDispatchOrderNumber,
  addProcessInspection,
  addProcessInspectionBatch
} from '@/api/mes/processing';
import { index } from './index';
const { proxy } = getCurrentInstance();
const { ProcessingData, ProcessingDataSizeData } = index();
// const orderData = ref();
const sizeData = ref();
let departmentOrderDataMaxWidth = ref();
let sizeDataMaxWidth = ref();
onMounted(() => {
  getTaskAssignProcessListByKeyWordFN();
  getSysSewingUndesirableReasonFN1();
  getSysSewingUndesirableReasonFN2();
  const right = new ResizeObserver((entries) => {
    for (const entry of entries) {
      departmentOrderDataMaxWidth.value = entry.contentRect.width * 0.52;
    }
  });
  const bottom = new ResizeObserver((entries) => {
    for (const entry of entries) {
      sizeDataMaxWidth.value = entry.contentRect.width - 20;
    }
  });
  const targetElementRight = document.getElementById('topboxID');
  const targetElementBottom = document.getElementById('bottomboxID');
  right.observe(targetElementRight);
  bottom.observe(targetElementBottom);
});
const departmentOrderDataRef = ref();
const keyword = ref('');
const getTaskAssignProcessListByKeyWordFN = () => {
  getTaskAssignProcessListByKeyWord(keyword.value).then((res) => {
    ProcessingData.data = res.data;
    if (res.data.length > 0) {
      orderDataRowDblclick({ row: res.data[0] });
      const $grid = departmentOrderDataRef.value;
      if ($grid && ProcessingData.data) {
        $grid.setCurrentRow(ProcessingData.data[0]);
      }
    } else {
      ProcessingDataSizeData.data = [];
      ProcessingDataSizeData.columns = [{ type: 'seq', field: '', width: 70, align: 'center' }];
    }
  });
};
const activeLeftForm = ref({});
const orderDataRowDblclick = ({ row }) => {
  activeLeftForm.value = row;
  ProcessingDataSizeData.loading = true;
  getProcessVerticalSizeDetailListByDispatchOrderNumber(row.dispatchOrderNumber).then((res) => {
    ProcessingDataSizeData.columns = [{ type: 'seq', field: '', width: 70, align: 'center' }];
    res.data.headers.map((item) => {
      ProcessingDataSizeData.columns.push({
        field: item,
        title: item,
        width: 100
      });
    });
    ProcessingDataSizeData.loading = false;
    const CustomizeData = [];
    res.data.rows.map((item, index) => {
      const obj = {};
      item.map((item1, index1) => {
        obj[res.data.headers[index1]] = item1;
      });
      CustomizeData.push(obj);
    });
    // console.log(ProcessingDataSizeData.columns);
    const tret = ProcessingDataSizeData.columns.slice(1).sort((a, b) => {
      const sizeA = parseFloat(a.title);
      const sizeB = parseFloat(b.title);
      return sizeA - sizeB;
    });
    // console.log(tret);
    ProcessingDataSizeData.columns = tret;
    ProcessingDataSizeData.data = CustomizeData;
  });
};
const checkDialogVisible = ref(false);
function isEmptyObject(obj) {
  return Object.keys(obj).length === 0;
}
const goCheck = () => {
  if (!isEmptyObject(activeLeftForm.value)) {
    checkDialogVisible.value = true;
    getSewingDefectDetailListFN(activeLeftForm.value.dispatchOrderNumber);
    getTopSewingDefectDetailFN(activeLeftForm.value.dispatchOrderNumber);
  } else {
    proxy?.$modal.msgWarning('请选择数据');
  }
};
const getSewingDefectDetailListFN = (id) => {
  getProcessDefectDetailList(id).then((res) => {
    defectStatistics.value = res.data;
  });
};
const getTopSewingDefectDetailFN = (id) => {
  getTopProcessDefectDetai(id).then((res) => {
    TopThreeDefects.value = res.data;
  });
};
import { Check, Close, Warning, Connection, Stamp, ScaleToOriginal, Crop, Link, Timer, Histogram } from '@element-plus/icons-vue';
const defectTypes = ref();
const selectedDefects = ref([]);
const currentGrade = ref('B');

const toggleDefect = (defect) => {
  const index = selectedDefects.value.indexOf(defect);
  if (index === -1) {
    selectedDefects.value.push(defect);
  } else {
    selectedDefects.value.splice(index, 1);
  }
};
const handlePass = () => {
  // 处理通过操作
};
const handleClose = () => {
  checkDialogVisible.value = false;
};
const otherDefects = ref();
const getSysSewingUndesirableReasonFN1 = () => {
  getSysSewingUndesirableReason(1, 'sys_process_undesirable_reason').then((res) => {
    // otherDefects.value = res.data;
    defectTypes.value = res.data;
  });
};
const getSysSewingUndesirableReasonFN2 = () => {
  getSysSewingUndesirableReason(2, 'sys_process_undesirable_reason').then((res) => {
    // defectTypes.value = res.data;
    otherDefects.value = res.data;
  });
};
const defectStatistics = ref([]);
const TopThreeDefects = ref([]);
const BondingGapArr = ref([]);
const BondingGapIsShow = ref(false);
let activeClickTenDefect;
let gradeType;
const clickTenDefect = (val) => {
  activeClickTenDefect = val;

  getSizeDetailListByDispatchOrderNumber(activeLeftForm.value.dispatchOrderNumber).then((res) => {
    console.log(res, 'res');
    BondingGapArr.value = res.data;
    BondingGapIsShow.value = true;
    // sizeData.value = res.data;
  });
};
const clickBondingGap = async (val) => {
  // console.log(val);
  const deepCopy = JSON.parse(JSON.stringify(val));
  if (gradeType == 'D' || gradeType == 'OTHER') {
    deepCopy.defectName = activeClickTenDefect.dictValue;
    deepCopy.dictCodeId = activeClickTenDefect.dictCode;
  }
  deepCopy.gradeType = gradeType;
  deepCopy.dispatchOrderNumber = activeLeftForm.value.dispatchOrderNumber;
  await addProcessInspection(deepCopy).then((res) => {
    proxy?.$modal.msgSuccess('操作成功');
  });
  console.log(gradeType, 'gradeType');
  BondingGapIsShow.value = false;
  if (gradeType == 'OTHER') {
    OTHERDEFECTSIsShow.value = false;
  }
  getSewingDefectDetailListFN(activeLeftForm.value.dispatchOrderNumber);
  getTopSewingDefectDetailFN(activeLeftForm.value.dispatchOrderNumber);
};
const OTHERDEFECTSIsShow = ref(false);
let defectTitle = ref('');
const isShowdepartmentOrderDataToolbarConfig = () => {
  ProcessingData.toolbarConfig.custom = !ProcessingData.toolbarConfig.custom;
};
const bujiaoIsShow = ref(false);
const selectedIndexes = ref([]);
const selectedDefect = ref([]);
const handleSelect = (index, defect) => {
  const idx = selectedIndexes.value.indexOf(index);
  defect.dispatchOrderNumber = activeLeftForm.value.dispatchOrderNumber;
  defect.gradeType = 'A';
  if (idx === -1) {
    selectedIndexes.value.push(index);
    selectedDefect.value.push(defect);
  } else {
    selectedIndexes.value.splice(idx, 1);
    selectedDefect.value.splice(idx, 1);
  }
};
const bujiaoPassWordIsShow = ref(false);
const passWord = ref();
const shougongdanSubmit = () => {
  addProcessInspectionBatch({ passWord: passWord.value, addMesProcessInspectionRequestDtoList: selectedDefect.value }).then((res) => {
    bujiaoPassWordIsShow.value = false;
    bujiaoIsShow.value = false;

    // proxy?.$modal.msgSuccess('提交成功');
    if (res.msg.split(';').length == 1) {
      proxy?.$modal.msgSuccess('提交成功');
    } else {
      const msgArr = res.msg.split(';');
      msgArr.map((item) => {
        proxy?.$modal.msgWarning(item);
      });
    }
  });
};
</script>
<style scoped>
.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.info-item span {
  font-size: 1rem;
  font-weight: 500;
}
@media (max-width: 1024px) {
  .info-item span:first-child {
    font-size: 0.75rem;
  }
  .info-item span:last-child {
    font-size: 0.875rem;
  }
}
.py-8 {
  padding: 5px;
}
.min-h-screen {
  height: calc(100vh - 20px);
}
.fa_box {
  height: calc(100vh - 90px);
}
.disabled {
  pointer-events: none;
  opacity: 0.65; /* 调整透明度使它看起来被禁用 */
  background-color: #f0f0f0; /* 可选：改变背景颜色 */
}
:deep(.el-dialog__body) {
  /* height: calc(100vh - 90px) !important; */
  /* max-height: calc(100vh - 90px) !important; */
  overflow-y: auto;
  /* padding: 20px; */
  /* background-color: #cfc; */
}
:deep(.check-dialog .el-dialog__body) {
  height: calc(100vh - 65px) !important;
  max-height: calc(100vh - 65px) !important;
  overflow-y: auto;
}
:deep(.check-dialog2 .el-dialog__body) {
  height: calc(100vh - 60px) !important;
  max-height: calc(100vh - 60px) !important;
  overflow-y: auto;
  .bg-gray-50 {
    height: 100% !important;
  }
}
/* 确保对话框内容区域的高度正确 */
:deep(.check-dialog) .bg-gray-50 {
  height: 100% !important;
}

:deep(.check-dialog) .container {
  height: 100% !important;
  max-width: 100vw;
}

:deep(.check-dialog) .grid {
  height: 100% !important;
}

/* 确保右侧表格区域的布局正确 */
:deep(.check-dialog) .col-span-4 {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
}

:deep(.check-dialog) .col-span-4 > div {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.check-dialog) .el-table {
  flex: 1;
  height: auto !important;
}

/* 表格容器样式 */
.table-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 默认不再使用 flex: 1，因为我们现在在元素上直接设置了 flex 比例 */
}

.table-container h2 {
  flex-shrink: 0;
  margin-bottom: 10px !important; /* 确保标题与表格之间的间距一致 */
}

.table-container .el-table {
  flex: 1;
  overflow: auto;
}

/* 确保在小屏幕上也保持正确的比例 */
@media (max-width: 768px) {
  .col-span-4 > div:first-child {
    flex: 0.65 !important;
  }

  .col-span-4 > div:last-child {
    flex: 0.35 !important;
  }
}

.gap-4 {
  gap: 10px;
}
.p-4 {
  padding: 10px;
}
.p-6 {
  padding: 1rem;
}
:deep(.vxe-table--render-default .vxe-body--column.col--ellipsis) {
  height: 30px !important;
}
:deep(.vxe-table--render-default .vxe-header--column.col--ellipsis) {
  height: 30px !important;
}
:deep(.el-table__cell) {
  padding: 0px 0px;
}
:deep(.el-table .el-table__header-wrapper th) {
  height: 13px !important;
}
fieldset {
  border: 1px solid #ccc;
  padding: 5px;
  /* 添加阴影效果 */
  box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.1);
  /* 添加圆角边框 */
  border-radius: 10px;
  width: 100% !important;
  height: 100% !important;
  box-sizing: border-box;
  max-width: 100%;
}
legend {
  font-weight: bold;
}
</style>
