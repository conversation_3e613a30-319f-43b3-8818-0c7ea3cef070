import { reactive } from 'vue';
export function index(dictTypes) {
  const inputFilterRender = reactive({
    name: 'VxeInput'
  });

  const departmentOrderData = reactive({
    id: 'departmentOrderData',
    height: '100%',
    emptyText: '没有更多数据了！',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: false
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    rowConfig: {
      isCurrent: true,
      isHover: true
    },
    loading: false,
    columns: [
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '150',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '款式编号',
        field: 'styleCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '派工编号',
        field: 'dispatchOrderNumber',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '线别',
        field: 'deptName',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      }
    ],
    data: []
  });
  const departmentSizeData = reactive({
    id: 'departmentOrderData',
    height: '100%',
    emptyText: '没有更多数据了！',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: false
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [
      { type: 'seq', field: '', width: 70, align: 'center' }
      // {
      //   width: '150',
      //   title: 'size',
      //   field: 'sizeName',
      //   align: 'center',
      //   sortable: true,
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
      // },
      // {
      //   width: '150',
      //   title: 'quantity',
      //   field: 'count',
      //   align: 'center',
      //   sortable: true,
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
      // },
      // {
      //   width: '150',
      //   title: 'inspected',
      //   field: 'inspired',
      //   align: 'center',
      //   sortable: true,
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
      // },
      // {
      //   width: '150',
      //   title: 'completion',
      //   field: 'completion',
      //   align: 'center',
      //   sortable: true,
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender,
      //   slots: { default: 'default_completion' }
      // },
    ],
    data: []
  });
  const processingPrint = reactive({
    id: 'processingPrint',
    height: '100%',
    emptyText: '没有更多数据了！',
    cellClassName({ row, column }) {
      if (column.field === 'printCount') {
        if (row.printCount >= 1) {
          console.log('row.printCount', row.printCount);
          return 'col-green';
        }
      }
      return null;
    },
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: true
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [
      {
        field: 'checkbox',
        type: 'checkbox',
        width: 60,
        fixed: 'left',
        align: 'center'
      },
      { type: 'seq', field: '', width: 70, align: 'center' },

      {
        width: '150',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '款式编号',
        field: 'styleCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '颜色',
        field: 'orderStyleColorName',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '尺码',
        field: 'orderStyleSizeName',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '小包数',
        field: 'packetCount',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '数量',
        field: 'quantity',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '针车码',
        field: 'sewingQrCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '打印次数',
        field: 'printCount',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      }
    ],
    data: []
  });
  return { departmentOrderData, departmentSizeData, processingPrint };
}
