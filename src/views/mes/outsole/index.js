import { reactive } from 'vue';
export function index(dictTypes) {
  const inputFilterRender = reactive({
    name: 'VxeInput'
  });
  const decideLeft = reactive({
    headerCellClassName({ column }) {
      if (column?.editRender?.changeColor === 'blue') {
        return 'col-blue';
      }
      return null;
    },
    id: 'decideLeft',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideLeft-modal'
      }
    },
    toolbarConfig: {
      custom: true
    },

    editConfig: {
      trigger: 'click',
      mode: 'row'
    },
    loading: true,
    columns: [
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '120',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        showOverflow: true,
        // sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },

      {
        width: '110',
        title: '颜色',
        field: 'orderStyleColorName',
        align: 'center',
        // sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },

      {
        width: '250',
        title: '尺码/派工数量',
        field: 'sizeOrNumber',
        align: 'center',
        children: []
      },
      {
        width: '100',
        title: '订单总数',
        field: 'totalCount',
        align: 'center',
        // sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '季节号',
        field: 'sessionType',
        align: 'center',
        // sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '250',
        title: '款式编号',
        field: 'styleCode',
        align: 'center',
        // sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      }
    ],
    data: []
  });
  const decideRight = reactive({
    headerCellClassName({ column }) {
      if (column.field === 'units') {
        return 'col-blue';
      }
      return null;
    },
    id: 'decideRight',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true,
      minWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: true
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    editConfig: {
      trigger: 'click',
      mode: 'row'
    },
    columns: [
      {
        field: 'checkbox',
        type: 'checkbox',
        width: 60,
        fixed: 'left',
        align: 'center'
      },
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '150',
        title: '部位名称',
        field: 'partName',
        align: 'center',
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '部件',
        field: 'units',
        align: 'center',
        editRender: { autofocus: '.el-input__inner' },
        slots: { edit: 'edit_TextInput' },
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '物料名称',
        field: 'materialName',
        align: 'center',
        // sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '负责人',
        field: 'taskUserName',
        align: 'center',
        // sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender,
        slots: { default: 'default_user' }
      }
    ],
    data: []
  });
  const decideBottom = reactive({
    id: 'decideBottom',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: true
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [
      {
        field: 'checkbox',
        type: 'checkbox',
        width: 60,
        fixed: 'left',
        align: 'center'
      },
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '150',
        title: '部件名称',
        field: 'partName',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '115',
        title: '尺码',
        field: 'orderStyleSizeName',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '130',
        title: '派工数量',
        field: 'assignQuantity',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '部件码',
        field: 'partQrCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender,
        slots: { default: 'default_qrCodeImage' }
      },
      {
        width: '150',
        title: '颜色',
        field: 'orderStyleColorName',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '负责',
        field: 'taskUserName',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '季节号',
        field: 'sessionType',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '200',
        title: '款式编号',
        field: 'styleCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      // {
      //   width: '150',
      //   title: '颜色',
      //   field: 'orderStyleColorName',
      //   align: 'center',
      //   sortable: true,
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
      // },

      {
        width: '150',
        title: '小包数',
        field: 'packetCount',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      },
      {
        width: '150',
        title: '打印次数',
        field: 'printCount',
        align: 'center',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: inputFilterRender
      }
    ],
    data: []
  });
  const decideProcessed = reactive({
    id: 'decideProcessed',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: true
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '115',
        title: '尺码',
        field: 'orderStyleSizeName',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '完工数量',
        field: 'assignQuantity',
        align: 'center',
        sortable: true
      },
      // {
      //   width: '130',
      //   title: '配套数量',
      //   field: 'storageQuantity',
      //   align: 'center',
      //   sortable: true
      // },
      // {
      //   width: '150',
      //   title: '部件码',
      //   field: 'partQrCode',
      //   align: 'center',
      //   sortable: true,
      //   slots: { default: 'default_qrCodeImage' }
      // },
      {
        width: '150',
        title: '颜色',
        field: 'orderStyleColorName',
        align: 'center',
        sortable: true
      },
      // {
      //   width: '150',
      //   title: '部位名称',
      //   field: 'partName',
      //   align: 'center',
      //   sortable: true
      // },
      // {
      //   width: '150',
      //   title: '部件',
      //   field: 'units',
      //   align: 'center',
      //   sortable: true
      // },
      {
        width: '150',
        title: '配套率',
        field: 'lv',
        align: 'center',
        sortable: true,
        slots: { default: 'default_Progress' }
      },
      {
        width: '150',
        title: '订单数量',
        field: 'sizeOrderCount',
        align: 'center',
        sortable: true
      },
      // {
      //   width: '150',
      //   title: '负责',
      //   field: 'taskUserName',
      //   align: 'center',
      //   sortable: true
      // },
      // {
      //   width: '150',
      //   title: '季节号',
      //   field: 'sessionType',
      //   align: 'center',
      //   sortable: true
      // },
      // {
      //   width: '200',
      //   title: '款式编号',
      //   field: 'styleCode',
      //   align: 'center',
      //   sortable: true
      // },
      {
        width: '150',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        sortable: true
      }

      // {
      //   width: '150',
      //   title: '小包数',
      //   field: 'packetCount',
      //   align: 'center',
      //   sortable: true
      // }
    ],
    data: []
  });
  const decideProcessed2 = reactive({
    id: 'decideProcessed2',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: true
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '115',
        title: '尺码',
        field: 'orderStyleSizeName',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '完工数量',
        field: 'assignQuantity',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '订单数',
        field: 'sizeOrderCount',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '良品完成数',
        field: 'totalCompletCount',
        align: 'center',
        sortable: true
      },
      {
        width: '150',
        title: '颜色',
        field: 'orderStyleColorName',
        align: 'center',
        sortable: true
      },
      {
        width: '200',
        title: '款式编号',
        field: 'styleCode',
        align: 'center',
        sortable: true
      },
      {
        width: '150',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        sortable: true
      },
      {
        width: '150',
        title: '派工完成率',
        field: 't1',
        align: 'center',
        sortable: true,
        slots: { default: 'default_Progress' }
      },
      {
        width: '150',
        title: '良品完成率',
        field: 't2',
        align: 'center',
        sortable: true,
        slots: { default: 'default_Progress' }
      }
      // {
      //   width: '150',
      //   title: '小包数',
      //   field: 'packetCount',
      //   align: 'center',
      //   sortable: true,
      //   filters: [{ data: '' }],
      //   filterRender: inputFilterRender
      // }
    ],
    data: []
  });
  const processedOutsole = reactive({
    id: 'processedOutsole',
    height: '100%',
    emptyText: '没有更多数据了！',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: true
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '115',
        title: '尺码',
        field: 'orderStyleSizeName',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '完工数量',
        field: 'completedQuantity',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '订单数量',
        field: 'sizeOrderCount',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '完成率',
        field: 't1',
        align: 'center',
        sortable: true,
        slots: { default: 'default_Progress' }
      },
      {
        width: '150',
        title: '颜色',
        field: 'orderStyleColorName',
        align: 'center',
        sortable: true
      },

      {
        width: '200',
        title: '款式编号',
        field: 'styleCode',
        align: 'center',
        sortable: true
      },
      {
        width: '150',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        sortable: true
      }
    ],
    data: []
  });
  const processedProcessing = reactive({
    id: 'processedProcessing',
    height: '100%',
    emptyText: '没有更多数据了！',
    border: true,
    showOverflow: true,
    keepSource: true,
    resizableConfig: {
      isDblclickAutoWidth: true
    },
    columnConfig: {
      resizable: true
    },
    customConfig: {
      mode: 'modal',
      storage: true,
      modalOptions: {
        height: '500px',
        className: 'decideRight-modal'
      }
    },
    toolbarConfig: {
      custom: true
    },
    scrollY: {
      enabled: true,
      gt: 0
    },
    scrollX: {
      enabled: true,
      gt: 0
    },
    loading: false,
    columns: [
      { type: 'seq', width: 70, align: 'center' },
      {
        width: '115',
        title: '尺码',
        field: 'orderStyleSizeName',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '完工数量',
        field: 'sizeOrderCount',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '采集数量',
        field: 'totalCompletCount',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '派工数量',
        field: 'assignQuantity',
        align: 'center',
        sortable: true
      },
      {
        width: '130',
        title: '完成率',
        field: 't1',
        align: 'center',
        sortable: true,
        slots: { default: 'default_Progress' }
      },
      {
        width: '130',
        title: '良品完成率',
        field: 't2',
        align: 'center',
        sortable: true,
        slots: { default: 'default_Progress' }
      },
      {
        width: '150',
        title: '颜色',
        field: 'orderStyleColorName',
        align: 'center',
        sortable: true
      },

      {
        width: '200',
        title: '款式编号',
        field: 'styleCode',
        align: 'center',
        sortable: true
      },
      {
        width: '150',
        title: '制令号',
        field: 'customStyleCode',
        align: 'center',
        sortable: true
      }
    ],
    data: []
  });
  return { decideLeft, decideRight, decideBottom, decideProcessed, decideProcessed2, processedOutsole, processedProcessing };
}
