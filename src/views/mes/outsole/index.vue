<template>
  <div class="decide_box" v-loading="showMask">
    <div class="query_box">
      <el-form :model="queryParams" ref="queryFormRef">
        <el-row :gutter="20">
          <!-- 进度选择 -->
          <el-col :xl="6" :lg="6" :md="6" :sm="6" :xs="24" style="margin-bottom: 5px">
            <el-form-item prop="customStyleCode">
              <el-select v-model="queryParams.customStyleCode" placeholder="请选择制令号" style="width: 100%" clearable>
                <el-option
                  v-for="item in scheduleOptions"
                  :key="item.customStyleCode"
                  :label="item.customStyleCode"
                  :value="item.customStyleCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 关键词输入 -->
          <el-col :xl="6" :lg="6" :md="6" :sm="6" :xs="24" style="margin-bottom: 5px">
            <el-form-item prop="keyWord">
              <el-input v-model="queryParams.keyWord" placeholder="请输入关键词" style="width: 100%" clearable></el-input>
            </el-form-item>
          </el-col>

          <!-- 日期范围选择 -->
          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24" style="margin-bottom: 5px">
            <el-form-item prop="startEndTime">
              <el-date-picker
                v-model="queryParams.startEndTime"
                type="daterange"
                unlink-panels
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                :shortcuts="shortcuts"
                style="width: 100%"
                clearable
                @focus="(e) => e.target.blur()"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="button">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="success" plain icon="Search" @click="getOrderStyleStitchingListFn">查询</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Close" @click="resetQuery">重置</el-button>
        </el-col>

        <el-col :span="1.5" style="display: flex; align-items: center">
          <el-button
            :type="mode == 1 ? 'primary' : ''"
            @click="
              mode = 1;
              activeName.name = card;
            "
            >卡片模式</el-button
          >
        </el-col>
        <el-col :span="1.5" style="display: flex; align-items: center">
          <el-button
            :type="mode == 2 ? 'primary' : ''"
            @click="
              mode = 2;
              activeName.name = list;
            "
            >列表模式</el-button
          >
        </el-col>
        <el-col :span="1.5" style="margin-left: auto">
          <el-button plain @click="opendesignate('f43c91b2-6be8-4b43-bd09-1db30adab6b8')" :disabled="CurrentlyCheckedId.length == 0"
            >贴底派工</el-button
          >
        </el-col>
      </el-row>
    </div>
    <component
      ref="dynamicComponent"
      :is="activeName.name"
      :cardOrTableArr="cardOrTableArr"
      @cardFn="cardFn"
      @LoadMore="LoadMore"
      @loadingComplete="loadingComplete"
      :LoadMoreLoadingIsShow="LoadMoreLoadingIsShow"
      :processedComponent="processedComponent"
      @openProcessed="openProcessed"
      :NewactiveClickArr1="NewactiveClickArr"
      :CurrentlyCheckedId="CurrentlyCheckedId"
      :activeClickstyleDocTreeIdArr="activeClickstyleDocTreeIdArr"
    ></component>
    <div class="pagination" v-if="mode != 3">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[30, 60, 90, 120]"
        layout="total, sizes, prev, pager,"
        :total="queryParams.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <Outsole
      v-if="OutsoleIsShow"
      @closeOutsole="closeOutsole"
      :OutsoleList="OutsoleList"
      :activeClickstyleDocTreeIdArr="activeClickstyleDocTreeIdArr"
      :taskConfigurationId="taskConfigurationId"
    ></Outsole>
    <processed
      v-if="processedIsShow"
      @closeProcessedDialog="closeProcessedDialog"
      :openprocessedNumber="openprocessedNumber"
      :openprocesseItem="openprocesseItem"
    ></processed>
  </div>
</template>
<script  setup name="Decide">
import QRCode from 'qrcode';
import { Search, Close, Refresh } from '@element-plus/icons-vue';
import designate from './components/designate.vue';
import delivery from './components/delivery.vue';
import card from './components/card.vue';
import processed from './components/processed.vue';
import overlay from './components/Overlay.vue';
import list from './components/list.vue';
import Outsole from './components/Outsole.vue';
import { getOrderStyleStitchingList } from '@/api/mes/decide';
import { getOrderStyleBondingDetail } from '@/api/mes/Outsole';
import { reactive } from 'vue';
import { Loading } from '@element-plus/icons-vue';
const { proxy } = getCurrentInstance();
let activeName = reactive({
  name: card
});
const buttons = [
  { id: 1, text: 'loading', name: markRaw(overlay) },
  { id: 2, text: '卡片模式', name: markRaw(card) },
  { id: 3, text: '已处理列表模式', name: markRaw(processed) },
  { id: 4, text: '列表模式', name: markRaw(list) }
];
const shortcuts = [
  {
    text: '整周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '半月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    }
  },
  {
    text: '一月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '半年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
      return [start, end];
    }
  }
];
const queryFormRef = ref();
const queryParams = ref({ schedule: '', pageNum: 1, pageSize: 30, total: 0 });
// 遮罩层状态
const showMask = ref(false);
const cardOrTableArr = ref([]);
const scheduleOptions = ref([]);
const mode = ref(1);
const componentMap = {
  1: card,
  2: list,
  3: processed
};
const getOrderStyleStitchingListFn = () => {
  // activeName.name = overlay;
  showMask.value = true;
  getOrderStyleStitchingList(queryParams.value)
    .then((res) => {
      scheduleOptions.value = res.data.records;
      cardOrTableArr.value = res.data.records;
      queryParams.value.total = res.data.total;
      showMask.value = false;
      proxy?.$modal.msgSuccess('查询成功');
      if (mode == 1) {
        activeName.name = card;
      } else if (mode == 2) {
        activeName.name = list;
      } else if (mode == 3) {
        activeName.name = processed;
      }
    })
    .catch((err) => {
      showMask.value = false;
      activeName.name = mode == 1 ? (activeName.name = card) : (activeName.name = card);
    });
};
let LoadMoreLoadingIsShow = ref(true);
const LoadMore = async () => {
  queryParams.value.pageNum += 1;

  await getOrderStyleStitchingList(queryParams.value).then((res) => {
    cardOrTableArr.value = [...cardOrTableArr.value, ...res.data.records];
    if (queryParams.value.pageNum * queryParams.value.pageSize >= res.data.total) {
      LoadMoreLoadingIsShow.value = false;
    }
    loadingComplete(LoadMoreLoadingIsShow.value);
  });
};
const dynamicComponent = ref();
const loadingComplete = (val) => {
  dynamicComponent.value.loadingComplete(val);
};

onMounted(() => {
  getOrderStyleStitchingListFn();
});
const resetQuery = () => {
  queryFormRef.value.resetFields();
  queryParams.value.pageNum = 1;
  cardOrTableArr.value = [];
  CurrentlyCheckedId.value = [];
  activeClickstyleDocTreeIdArr.value = [];
  dynamicComponent.value.clearBorder();
  getOrderStyleStitchingListFn();
  console.log(queryParams.value);
};
let taskConfigurationId = ref({});
const OutsoleIsShow = ref(false);
const opendesignate = (val) => {
  taskConfigurationId.value.mesTaskConfigurationId = val;
  if (val == 'f43c91b2-6be8-4b43-bd09-1db30adab6b8') {
    OutsoleIsShow.value = true;
    getOrderStyleBondingDetail(CurrentlyCheckedId.value).then((res) => {
      OutsoleList.value = res.data;
    });
  }
};

//当前勾选的数据
const CurrentlyCheckedId = ref([]);
const activeClickstyleDocTreeIdArr = ref([]);
const NewactiveClickArr = ref([]);
const cardFn = (ids, styleDocTreeId, NewactiveClickArr1) => {
  CurrentlyCheckedId.value = ids;
  activeClickstyleDocTreeIdArr.value = styleDocTreeId;
  NewactiveClickArr.value = NewactiveClickArr1;
  // console.log(ids, styleDocTreeId, NewactiveClickArr1);
};

const inputFilterRender = reactive({
  name: 'VxeInput'
});
const handleSizeChange = (val) => {
  getOrderStyleStitchingListFn();
  console.log(`${val} items per page`);
};
const handleCurrentChange = (val) => {
  getOrderStyleStitchingListFn();
  console.log(`current page: ${val}`);
};
const processedComponent = ref(1);
const processedExamine = (val) => {
  mode.value = 3;
  activeName.name = processed;
  processedComponent.value = val;
};

const closeOutsole = () => {
  OutsoleIsShow.value = false;
};
const OutsoleList = ref();
const processedExamineid = ref(1);
const processedIsShow = ref(false);
let openprocessedNumber = ref();
let openprocesseItem = ref();
const openProcessed = (val, item) => {
  processedIsShow.value = true;
  openprocessedNumber.value = val;
  openprocesseItem.value = item;
};

// 添加关闭处理函数
const closeProcessedDialog = (val) => {
  processedIsShow.value = val;
};
</script>
<style lang='scss' scoped>
.decide_box {
  min-height: calc(100vh - 84px);
  padding: 10px 0px 0px 20px;
  .card_box {
    margin-top: 10px;
    min-height: calc(100vh - 184px);
    .default_card {
      padding: 10px;
      height: 300px;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); /* 主要的阴影效果 */
      transition: box-shadow 0.3s ease; /* 阴影过渡效果 */
      .top_box {
        // height: 150px;
        display: flex;
        justify-content: space-between;
        .top_box_left {
          width: 49%;
          height: 180px;
          background-color: aquamarine;
        }
        .top_box_right {
          width: 49%;
          height: 150px;
          p {
            margin: 8px 0;
          }
        }
      }
    }
    .Select_card {
      border: 2px solid #409eff;
    }
  }
}
:deep(.el-form-item) {
  margin-bottom: 2px !important;
}
.decideLeftBox {
  .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
  .vxe-table--render-default .vxe-header--column:not(.col--ellipsis),
  .vxe-table--render-default.is--padding .vxe-body--column:not(.col--ellipsis) {
    padding: 0px !important;
  }
  :deep(.vxe-table--render-default) {
    padding: 0px !important;
  }
  :deep(.vxe-footer--column:not(.col--ellipsis)) {
    padding: 0px !important;
  }
  :deep(.vxe-header--column:not(.col--ellipsis)) {
    padding: 1px !important;
  }
}

:deep(.el-dialog__header) {
  padding: 10px !important;
}
:deep(.el-dialog__body) {
  padding: 0px 0px !important;
}
:deep(.el-dialog__body) {
  max-height: calc(100vh - 100px) !important;
}
:deep(.el-overlay .el-overlay-dialog .el-dialog .el-dialog__body) {
  padding: 2px !important;
}
.elmBlue {
  color: #409eff;
}
/* 翻页动画样式 */
.flip-container {
  perspective: 1000px;
  position: relative;
  // height: 540px !important;
  height: calc(100vh - 180px) !important;
}

.flipper {
  transition: 0.3s;
  transform-style: preserve-3d;
  position: relative;
  height: 100%;
}

.front,
.back {
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.front {
  z-index: 2;
  transform: rotateY(0deg);
}

.back {
  transform: rotateY(180deg);
}

.flip-container.flipped .flipper {
  transform: rotateY(180deg);
}

/* 原有布局样式保持 */
.one {
  display: flex;
  justify-content: space-between;
  // height: 540px;
  height: 100%;
}

.mylist-table {
  height: 100% !important;
}
::v-deep(.mylist-table-left.vxe-grid .vxe-header--column.col-blue) {
  color: #409eff;
}
::v-deep(.mylist-table-right.vxe-grid .vxe-header--column.col-blue) {
  color: #409eff;
}
::v-deep(.decideLeft-modal) {
  div {
    top: 0px !important;
  }
}
::v-deep(.decideRight-modal) {
  div {
    top: 0px !important;
  }
}
.pagination {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: right;
  padding-right: 20px;
}
</style>
