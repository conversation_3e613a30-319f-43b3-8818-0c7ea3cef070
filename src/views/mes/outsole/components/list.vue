<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="table23">
      <!-- <vxe-grid class="mylist-table2" ref="decideProcessedRef" v-bind="gridOptions" height="100%" empty-text="没有数据!"> </vxe-grid> -->
      <vxe-grid ref="gridOptionsRef" v-bind="gridOptions" :key="listKey" empty-text="没有数据!" @checkboxChange="checkboxChange">
        <template #default_customName="{ row }">{{ row.mesOrderStyle?.customName }}</template>
        <template #default_sessionType="{ row }">{{ row.mesOrderStyle?.sessionType }}</template>
        <template #default_styleImageBase64="{ row }">
          <el-image
            :src="row.mesOrderStyle?.styleImageBase64"
            :zoom-rate="0.5"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[row.mesOrderStyle?.styleImageBase64]"
            show-progress
            :initial-index="4"
            :hide-on-click-modal="true"
            fit="contain"
            class="h-full w-full rounded object-cover"
        /></template>
        <template #default_decide="{ row }">
          <p>{{ row.mesTaskAssignList[0].taskUserName }}</p>
          <p>{{ row.mesTaskAssignList[0].startTime }}</p>
        </template>
        <template #default_sewingMachine="{ row }">
          <p>{{ row.mesTaskAssignList[1].taskUserName }}</p>
          <p>{{ row.mesTaskAssignList[1].startTime }}</p>
        </template>
        <template #default_outsole="{ row }">
          <p>{{ row.mesTaskAssignList[2].taskUserName }}</p>
          <p>{{ row.mesTaskAssignList[2].startTime }}</p>
        </template>
        <template #default_process="{ row }">
          <p>{{ row.mesTaskAssignList[3].taskUserName }}</p>
          <p>{{ row.mesTaskAssignList[3].startTime }}</p>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>
<script  setup>
import { reactive, watch } from 'vue';
const props = defineProps({
  cardOrTableArr: {},
  LoadMoreLoadingIsShow: {},
  NewactiveClickArr1: {}
});
const emits = defineEmits(['cardFn', 'LoadMore', 'loadingComplete']);
const inputFilterRender = reactive({
  name: 'VxeInput'
});
const imgUrlCellRender = reactive({
  name: 'VxeImage',
  props: {
    width: 36,
    height: 36
  }
});
const listKey = ref(Math.random());
const gridOptions = reactive({
  height: '100%',
  id: 'decideList',
  border: true,
  rowConfig: {
    useKey: true
  },
  tooltipConfig: {
    showAll: true,
    enterable: true,
    contentMethod: ({ type, column, row, items, _columnIndex }) => {
      return null;
    }
  },
  showOverflow: true,
  keepSource: true,
  resizableConfig: {
    isDblclickAutoWidth: true,
    minWidth: true
  },
  columnConfig: {
    useKey: true,
    resizable: true
  },
  customConfig: {
    mode: 'modal',
    storage: true,
    modalOptions: {
      height: '500px',
      className: 'decideRight-modal'
    }
  },
  toolbarConfig: {
    custom: true
  },
  scrollY: {
    enabled: true,
    gt: 0
  },
  scrollX: {
    enabled: true,
    gt: 0
  },
  columns: [
    {
      // field: 'checkbox',
      type: 'checkbox',
      width: 60,
      fixed: 'left',
      align: 'center'
    },
    { align: 'center', type: 'seq', field: '', width: 70 },
    {
      align: 'center',
      field: 'customName',
      title: '品牌',
      width: 110,
      filters: [{ data: '' }],
      filterRender: inputFilterRender,
      slots: { default: 'default_customName' }
    },
    { align: 'center', field: 'customStyleCode', title: '制令号', width: 110, filters: [{ data: '' }], filterRender: inputFilterRender },
    { align: 'center', field: 'styleCode', title: '款式编号', width: 110, filters: [{ data: '' }], filterRender: inputFilterRender },
    {
      align: 'center',
      field: 'sessionType',
      title: '季节',
      width: 110,
      filters: [{ data: '' }],
      filterRender: inputFilterRender,
      slots: { default: 'default_sessionType' }
    },
    { align: 'center', field: 'totalCount', title: '订单数量', width: 110, filters: [{ data: '' }], filterRender: inputFilterRender },
    { align: 'center', field: 'styleImageBase64', title: '图片', width: 80, slots: { default: 'default_styleImageBase64' } },
    { align: 'center', field: 'styleImageBase642', title: '裁断', width: 120, slots: { default: 'default_decide' } },
    { align: 'center', field: 'styleImageBase643', title: '针车', width: 120, slots: { default: 'default_sewingMachine' } },
    { align: 'center', field: 'styleImageBase644', title: '大底', width: 120, slots: { default: 'default_outsole' } },
    { align: 'center', field: 'styleImageBase645', title: '加工', width: 120, slots: { default: 'default_process' } }
    // { field: 'address', title: '图片' }
  ],
  data: props.cardOrTableArr
});
const gridOptionsRef = ref(null);
// 监听 cardOrTableArr 的变化，并更新 gridOptions.data
watch(
  () => props.cardOrTableArr,
  (newVal) => {
    if (newVal) {
      gridOptions.data = newVal;
      const $grid = gridOptionsRef.value;
      if ($grid && props.NewactiveClickArr1.length > 0) {
        props.NewactiveClickArr1.map((item) => {
          $grid.setCheckboxRow(item, true);
          // console.log(item);
        });
      }
    }
  },
  { deep: true, immediate: true }
);
const loadingIsShow = ref(props.LoadMoreLoadingIsShow);

const handleLoadingComplete = (val) => {
  gridOptions.loading = false;
  loadingIsShow.value = val;
};
defineExpose({
  loadingComplete: handleLoadingComplete
});
const checkboxChange = ({ row, rowIndex }) => {
  // console.log(row, rowIndex);
  setTimeout(() => {
    const $grid = gridOptionsRef.value;
    if ($grid) {
      const selectRecords = $grid.getCheckboxRecords();
      console.log(selectRecords);
      // $grid.setCurrentRow(row);
      const activeClickArr = [];
      const activeClickstyleDocTreeIdArr = [];
      selectRecords.forEach((item) => {
        activeClickArr.push(item.mesOrderStyleStitchingListId);
        activeClickstyleDocTreeIdArr.push(item.styleDocTreeId);
      });
      emits('cardFn', activeClickArr, activeClickstyleDocTreeIdArr, selectRecords);
    }
  }, 200);
};
onMounted(() => {
  const $grid = gridOptionsRef.value;
  $grid.clearCheckboxRow();
  console.log(props.NewactiveClickArr1);
  if ($grid && props.NewactiveClickArr1.length > 0) {
    props.NewactiveClickArr1.map((item) => {
      $grid.setCheckboxRow(item, true);
      // console.log(item);
    });
  }
});
</script>
<style lang='scss' scoped>
.min-h-screen {
  min-height: calc(100vh - 234px);
  max-height: calc(100vh - 234px);
  overflow: hidden;
  overflow-y: auto;
}
.py-8 {
  padding: 10px 0px !important;
}
::v-deep(.decideRight-modal) {
  div {
    top: 0px !important;
  }
}
.table23 {
  height: calc(100vh - 255px);
}
</style>
