<template>
  <div>
    <!-- <el-dropdown class="dr-box" style="margin-left: 0px" @command="examine" :disabled="disabled">
      <el-button :disabled="disabled" @click="single(list[0])">
        <el-icon> <Avatar /> </el-icon>{{ list[0]?.taskName }}<el-icon class="el-icon--right"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item :command="item" v-for="item in list" :key="item.mesTaskConfigurationId">{{ item.taskName }}</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown> -->
    <el-select v-model="mesTaskConfiguration" :disabled="disabled" style="width: 100px">
      <el-option
        v-for="item in list"
        :key="item.mesTaskConfigurationId"
        :label="item.taskName"
        :value="item.mesTaskConfigurationId"
        @click="test(item)"
      />
    </el-select>
  </div>
</template>
<script  setup>
const emits = defineEmits(['opendesignate']);
import { gettaskConfigurationlist, getTaskConfigurationListByRoleIds } from '@/api/mes/decide';
import { useUserStore } from '@/store/modules/user';
// 接收disabled属性，用于控制按钮是否禁用
const props = defineProps({
  disabled: {
    type: Boolean,
    default: true
  }
});
const mesTaskConfiguration = ref('');
const list = ref([]);
onMounted(() => {
  gettaskConfigurationlist().then((res) => {
    // list.value = res.rows;
  });
  const roleIdArr = useUserStore().roleIdArr;
  getTaskConfigurationListByRoleIds(roleIdArr).then((res) => {
    // console.log(res)
    list.value = res.data;
    mesTaskConfiguration.value = res.data[0].mesTaskConfigurationId;
  });
});
const examine = (val) => {
  console.log(val);
  // emits('opendesignate', val);
};
const single = (val) => {
  emits('opendesignate', val);
  console.log(val);
};
const test = (val) => {
  console.log(val);
  emits('opendesignate', val);
};
</script>
<style lang='scss' scoped>
</style>
