<template>
  <div>
    <el-dialog v-model="OutsoleDialogVisible" :fullscreen="true" title="贴底派工" class="check-dialog" :before-close="closeOutsole">
      <div class="automaticHeight">
        <div class="content">
          <div style="display: flex; justify-content: right; align-items: center">
            <!-- <span style="margin-right: 10px;">计划完成时间：<span style="color: #409EFF">{{ props?.OutsoleList[0].shipmentDate }}</span></span> -->
            <span style="margin-right: 10px" v-if="!isShowCode">每箱双数：<el-input v-model="sys_cofig_box_numberValue" style="width: 100px" /></span>
            <span style="margin-right: 10px" v-if="!isShowCode"> <el-button type="primary" @click="generateBoxes">生成箱码</el-button></span>
            <span style="margin-right: 5px" v-if="isShowCode">
              <el-button type="primary" @click="SwitchDecideBottom">
                {{ SwitchDecideBottomId == 1 ? '切换已打印' : SwitchDecideBottomId == 2 ? '切换全部' : '切换未打印' }}</el-button
              ></span
            >
            <span
              ><el-button type="primary" @click="toggleView">{{ isShowCode ? '返回列表' : '查看箱码列表' }}</el-button></span
            >
          </div>
          <div class="flip-container" :class="{ 'flip': isShowCode }">
            <div class="flipper">
              <div class="front">
                <vxe-grid
                  class="mylist-table-outsole"
                  ref="outsoleRef"
                  v-bind="Outsole"
                  @checkboxChange="decideRightCheckboxChange"
                  @checkboxAll="decideRightCheckboxAll"
                  :key="OutsoleKey"
                >
                  <template #default_user="{ row, rowIndex }">
                    <el-button v-if="!row.taskUserId && row.isShowModify" type="primary" link @click="openUserList(row)">选择</el-button>
                    <span v-else
                      >{{ row.taskUserName }}
                      <el-button v-if="row.ModifyUser" type="primary" link @click="openUserList(row, true)">重选</el-button>
                      <el-button v-if="row.taskUserId" link @click="synchronizationUser(row, rowIndex)"
                        ><el-icon color="#409EFF"><Switch /></el-icon></el-button
                    ></span>
                  </template>
                  <template #edit_assignQuantityt="{ row, column }">
                    <el-input-number v-model="row[column.property]" :disabled="row.isGeneratedComponentCode == 1" />
                  </template>
                  <template #edit_taskUserName="{ row, rowIndex }">
                    <el-button v-if="!row.taskUserId && row.isShowModify" type="primary" link @click="openUserList(row)">选择</el-button>
                    <span v-else
                      >{{ row.taskUserName }}
                      <el-button v-if="row.ModifyUser" type="primary" link @click="openUserList(row, true)">重选</el-button>
                      <el-button v-if="row.taskUserId" link @click="synchronizationUser(row, rowIndex)"
                        ><el-icon color="#409EFF"><Switch /></el-icon></el-button
                    ></span>
                  </template>
                </vxe-grid>
              </div>
              <div class="back">
                <vxe-grid class="mylist-table-outsole" ref="outsole2Ref" v-bind="GeneratedBondingCodeDetail" :key="GeneratedBondingCodeDetailKey">
                  <template #default_qrCodeImage="{ row, column }">
                    <span
                      >{{ row[column.property]
                      }}<el-button
                        :type="row.type ? 'success' : 'primary'"
                        link
                        @click="
                          openQrCodeImage(row.bondingQrCode);
                          row.type = 'success';
                        "
                        >打开</el-button
                      ></span
                    >
                  </template>
                </vxe-grid>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeOutsole">关闭</el-button>
          <el-button type="primary" @click="goReprintCode" v-if="isShowCode">补打条码</el-button>

          <el-button type="primary" @click="handlePrint" v-if="isShowCode">打印条码</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="userListDialog" title="大底用户列表" width="85%">
      <span
        ><el-table :data="userListData" border height="500px" @row-dblclick="userListDataDblclick">
          <el-table-column property="userName" label="用户昵称" />
          <el-table-column property="deptName" label="部门" /> </el-table
      ></span>
    </el-dialog>
    <el-dialog v-model="QrCodeImagedialogVisible" title="二维码" width="500" :close-on-click-modal="true">
      <span><img :src="qrCodeImage" alt="QR Code" /></span>
    </el-dialog>
    <el-dialog v-model="ReprintCodeDialog" title="补打条码" width="80%">
      <span>
        <el-input-number v-model="ReprintCodeNumber" :min="1" :max="ReprintCodeMaxNumber" />
      </span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="ReprintCodeDialog = false">关闭</el-button>
          <el-button type="primary" @click="SureReprintCode"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script  setup>
import QRCode from 'qrcode';
import {
  sys_cofig_box_number,
  creatGeneratedBondingCode,
  getGeneratedBondingCodeDetail,
  getUserListByTaskName,
  printBondingCode,
  reprintBondingCode
} from '@/api/mes/Outsole';
// import { reprintMesTaskAssignCuttingCode } from '@/api/mes/decide';
import { reactive } from 'vue';
const { proxy } = getCurrentInstance();
const emits = defineEmits(['closeOutsole']);
const props = defineProps({
  OutsoleList: {},
  activeClickstyleDocTreeIdArr: {},
  taskConfigurationId: {}
});

const OutsoleDialogVisible = ref(true);
const inputFilterRender = reactive({
  name: 'VxeInput'
});
const OutsoleKey = ref(Math.random());
const Outsole = reactive({
  headerCellClassName({ column }) {
    if (column.field === 'assignQuantity') {
      return 'col-blue';
    }
    return null;
  },
  id: 'TaskListOutsole',
  border: true,
  height: '100%',
  rowConfig: {
    useKey: true
  },
  tooltipConfig: {
    showAll: true,
    enterable: true,
    contentMethod: ({ type, column, row, items, _columnIndex }) => {
      return null;
    }
  },
  showOverflow: true,
  keepSource: true,
  resizableConfig: {
    isDblclickAutoWidth: true,
    minWidth: true
  },
  columnConfig: {
    useKey: true,
    resizable: true
  },
  customConfig: {
    mode: 'modal',
    storage: true,
    modalOptions: {
      height: '500px',
      className: 'decideRight-modal'
    }
  },
  toolbarConfig: {
    custom: true
  },
  scrollY: {
    enabled: true,
    gt: 0
  },
  scrollX: {
    enabled: true,
    gt: 0
  },
  loading: false,
  editConfig: {
    trigger: 'click',
    mode: 'row'
  },
  editRules: {
    taskUserName: [{ required: true, message: '必须填写' }]
  },
  columns: [
    { field: 'checkbox', type: 'checkbox', width: 60, fixed: 'left', align: 'center' },
    { type: 'seq', field: 'test', width: 70, align: 'center' },
    {
      width: '150',
      title: '季节号',
      field: 'sessionType',
      align: 'center',
      sortable: true
    },
    {
      width: '115',
      title: '款式编号',
      field: 'styleCode',
      align: 'center',
      sortable: true
    },
    {
      width: '130',
      title: '制令号',
      field: 'customStyleCode',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '颜色',
      field: 'orderStyleColorName',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '尺码',
      field: 'orderStyleSizeName',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '订单数量',
      field: 'sizeCount',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '未派工数量',
      field: 'assignQuantity',
      align: 'center',
      sortable: true,
      editRender: { autofocus: '.el-input__inner' },
      slots: { edit: 'edit_assignQuantityt' }
    },
    {
      width: '150',
      title: '组别',
      field: 'deptName',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '负责人',
      field: 'taskUserName',
      align: 'center',
      sortable: true,
      editRender: { autofocus: '.el-input__inner' },
      slots: { default: 'default_user', edit: 'edit_taskUserName' }
    }
  ],
  data: []
});
const GeneratedBondingCodeDetail = reactive({
  id: 'GeneratedBondingCodeDetail',
  border: true,
  height: '100%',
  rowConfig: {
    useKey: true
  },
  tooltipConfig: {
    showAll: true,
    enterable: true,
    contentMethod: ({ type, column, row, items, _columnIndex }) => {
      return null;
    }
  },
  showOverflow: true,
  keepSource: true,
  resizableConfig: {
    isDblclickAutoWidth: true,
    minWidth: true
  },
  columnConfig: {
    useKey: true,
    resizable: true
  },
  customConfig: {
    mode: 'modal',
    storage: true,
    modalOptions: {
      height: '500px',
      className: 'decideRight-modal'
    }
  },
  toolbarConfig: {
    custom: true
  },
  scrollY: {
    enabled: true,
    gt: 0
  },
  scrollX: {
    enabled: true,
    gt: 0
  },
  loading: false,
  editConfig: {
    trigger: 'click',
    mode: 'row'
  },
  columns: [
    {
      field: 'checkbox',
      type: 'checkbox',
      width: 60,
      fixed: 'left',
      align: 'center'
    },
    { type: 'seq', field: 'cheada', width: 70 },
    {
      width: '150',
      title: '季节号',
      field: 'sessionType',
      align: 'center',
      sortable: true
    },
    {
      width: '115',
      title: '款式编号',
      field: 'styleCode',
      align: 'center',
      sortable: true
    },
    {
      width: '130',
      title: '制令号',
      field: 'customStyleCode',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '颜色',
      field: 'orderStyleColorName',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '尺码',
      field: 'orderStyleSizeName',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '订单数量',
      field: 'totalCount',
      align: 'center',
      sortable: true
    },
    {
      width: '150',
      title: '派工数',
      field: 'assignQuantity',
      align: 'center',
      sortable: true,
      editRender: { name: 'VxeInput', props: { clearable: true } }
    },
    {
      width: '150',
      title: '箱数',
      field: 'packetCount',
      align: 'center',
      sortable: true,
      editRender: { name: 'VxeInput', props: { clearable: true } }
    },
    {
      width: '150',
      title: '组别',
      field: 'deptName',
      align: 'center',
      sortable: true,
      editRender: { name: 'VxeInput', props: { clearable: true } }
    },
    {
      width: '150',
      title: '负责人',
      field: 'taskUserName',
      align: 'center',
      sortable: true,
      editRender: { name: 'VxeInput', props: { clearable: true } }
    },
    {
      width: '250',
      title: '箱码',
      field: 'bondingQrCode',
      align: 'center',
      sortable: true,
      slots: { default: 'default_qrCodeImage' }
    },
    {
      width: '150',
      title: '打印次数',
      field: 'printCount',
      align: 'center',
      sortable: true
    }
  ],
  data: []
});
watch(
  () => props.OutsoleList,
  (newVal) => {
    if (newVal) {
      Outsole.data = newVal;
      const filtersFields = [
        'sessionType',
        'styleCode',
        'customStyleCode',
        'orderStyleColorName',
        'orderStyleSizeName',
        'sizeCount',
        'deptName',
        'taskUserName',
        'assignQuantity'
      ];
      proxy.filtersFieldsFn(filtersFields, Outsole);
    }
  },
  { deep: true, immediate: true }
);

const closeOutsole = () => {
  OutsoleDialogVisible.value = false;
  emits('closeOutsole');
};
const isShowCode = ref(false);

const toggleView = () => {
  isShowCode.value = !isShowCode.value;
  if (isShowCode.value) {
    getGeneratedBondingCodeDetailFN();
  }
};

onMounted(() => {
  sys_cofig_box_numberFn();

  setTimeout(() => {
    OutsoleKey.value = Math.random();
    console.log(props);
  }, 500);
});
let sys_cofig_box_numberValue = ref('');

const sys_cofig_box_numberFn = () => {
  sys_cofig_box_number().then((res) => {
    sys_cofig_box_numberValue.value = res.data;
  });
};

const outsoleRef = ref(null);
//生成箱数
const generateBoxes = () => {
  // const $grid = outsoleRef.value;
  // const selectRecordstest = $grid.getCheckboxRecords();
  // for (let i = 0; i < selectRecordstest.length; i++) {
  //   if (!selectRecordstest[i].taskUserName) {
  //     proxy.$modal.msgError(`${i + 1}行请选择负责人`);
  //     return;
  //   }
  // }
  // const f = selectRecordstest.filter((item) => !item.taskUserName);
  // console.log(selectRecordstest);
  // console.log(f);
  // return;
  const selectRecords = Outsole.data.filter((item) => item.isShowModify);
  console.log(selectRecords);
  selectRecords.map((item) => {
    item.packetNumber = Number(sys_cofig_box_numberValue.value);
    item.mesTaskConfigurationId = props.taskConfigurationId.mesTaskConfigurationId;
  });
  if (selectRecords.length == 0) {
    proxy.$modal.msgError('请选择数据');
    return;
  }
  const loading = ElLoading.service({
    lock: true,
    text: '生成中...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  creatGeneratedBondingCode(selectRecords)
    .then((res) => {
      Outsole.data.map((item) => {
        item.isShowModify = false;
        item.ModifyUser = false;
      });
      proxy?.$modal.msgSuccess('生成成功');
      loading.close();
      toggleView();
    })
    .catch((err) => {
      loading.close();
    });
};
const GeneratedBondingCodeDetailKey = ref(Math.random());
const getGeneratedBondingCodeDetailFN = (id) => {
  getGeneratedBondingCodeDetail(props.activeClickstyleDocTreeIdArr[0]).then((res) => {
    // GeneratedBondingCodeDetail.data = res.data;
    GeneratedBondingCodeDetail.data = SwitchDecideBottomFn(res.data);
    const filtersFields = [
      'sessionType',
      'styleCode',
      'customStyleCode',
      'orderStyleColorName',
      'orderStyleSizeName',
      'sizeCount',
      'deptName',
      'taskUserName',
      'packetCount',
      'assignQuantity',
      'totalCount'
    ];
    proxy.filtersFieldsFn(filtersFields, GeneratedBondingCodeDetail);

    setTimeout(() => {
      GeneratedBondingCodeDetailKey.value = Math.random();
    }, 100);
  });
};
const userListDialog = ref(false);
const userListData = ref([]);
let activeDecideRightRow;
const ReselectIsShow = ref(false);
const openUserList = (row, Reselect) => {
  activeDecideRightRow = row;
  ReselectIsShow.value = Reselect;
  userListDialog.value = true;
  getUserListByTaskName(props.taskConfigurationId.mesTaskConfigurationId).then((res) => {
    userListData.value = res.data;
  });
};
const userListDataDblclick = (row) => {
  activeDecideRightRow.taskUserName = row.userName;
  activeDecideRightRow.taskUserId = row.userId;
  activeDecideRightRow.deptId = row.deptId;
  activeDecideRightRow.deptName = row.deptName;
  userListDialog.value = false;
  const $grid = outsoleRef.value;
  const selectRecords = $grid.getCheckboxRecords();
  selectRecords.map((item) => {
    item.taskUserName = row.userName;
    item.taskUserId = row.userId;
    item.deptId = row.deptId;
    item.deptName = row.deptName;
    item.isShowModify = true;
    item.ModifyUser = false;
  });
  $grid.clearCheckboxRow();
  userListDialog.value = false;
};
const synchronizationUser = (row, index) => {
  console.log(row, index);
  //切割Outsole.data 从index开始的数组
  Outsole.data.slice(index).map((item) => {
    console.log(item);
    item.taskUserName = row.taskUserName;
    item.taskUserId = row.taskUserId;
    item.deptId = row.deptId;
    item.deptName = row.deptName;
  });
  // console.log(Outsole.data);
};

const qrCodeImage = ref();
const QrCodeImagedialogVisible = ref(false);
const openQrCodeImage = (val) => {
  QRCode.toDataURL(val, (err, url) => {
    console.log(url);
    QrCodeImagedialogVisible.value = true;
    qrCodeImage.value = url;
  });
};
const outsole2Ref = ref(null);
const handlePrint = () => {
  if (!isShowCode.value) {
    isShowCode.value = true;
    getGeneratedBondingCodeDetailFN();
  } else {
    const $grid = outsole2Ref.value;
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length === 0) {
      proxy?.$modal.msgWarning('请勾选需要打印的数据');
      return;
    }
    printBondingCode(selectRecords).then((res) => {
      proxy?.$modal.msgSuccess('打印成功');
    });
  }
};
const ReprintCodeDialog = ref(false);
const ReprintCodeNumber = ref(1);
let ReprintCodeMaxNumber = ref(0);
const goReprintCode = () => {
  ReprintCodeNumber.value = 1;
  const $grid = outsole2Ref.value;
  const selectRecords = $grid.getCheckboxRecords();
  if (selectRecords.length == 0) {
    proxy?.$modal.msgWarning('请选择一条数据');
    return;
  }
  ReprintCodeDialog.value = true;
  // let minRatio = 1;
  // selectRecords.forEach((record) => {
  //   const currentRatio = record.assignQuantityc / record.packetCount;
  //   console.log(currentRatio, record.assignQuantity, record.packetCount);
  //   // if (currentRatio < minRatio) {
  //   //   minRatio = currentRatio;
  //   // }
  // });
  // console.log(minRatio);
  const minPacketCount = selectRecords.reduce((min, record) => {
    // const ratio = record.assignQuantity / record.packetCount;
    const ratio = record.packetCount;
    // console.log(ratio, record.assignQuantity, record.packetCount);
    console.log(min, ratio);
    return ratio < min ? ratio : min;
  }, Infinity);
  ReprintCodeMaxNumber.value = minPacketCount;
  // ReprintCodeMaxNumber.value = 1;
  // console.log(ReprintCodeMaxNumber.value);
};
const SureReprintCode = () => {
  const $grid = outsole2Ref.value;
  const selectRecords = $grid.getCheckboxRecords();

  const deppselectRecords = JSON.parse(JSON.stringify(selectRecords));
  deppselectRecords.map((item) => {
    item.assignQuantity = item.assignQuantity / item.packetCount;
    item.packetCount = 1;
  });
  reprintBondingCode({ printCount: ReprintCodeNumber.value, bondingCodelist: deppselectRecords }).then((res) => {
    ReprintCodeDialog.value = false;
  });

  console.log(selectRecords);
};
const decideRightCheckboxAll = (val) => {
  console.log(val.records);
  if (val.records.length == 0) {
    Outsole.data.map((item) => {
      // item.isShowModify = false;
      item.ModifyUser = false;
    });
  } else {
    val.records.map((item) => {
      // item.isShowModify = true;
      // item.ModifyUser = false;
      item.ModifyUser = true;
    });
  }
};
const decideRightCheckboxChange = (val) => {
  // val.row.isShowModify = !val.row.isShowModify;
  val.row.ModifyUser = !val.row.ModifyUser;
};
const SwitchDecideBottomId = ref(1);
const SwitchDecideBottom = () => {
  if (SwitchDecideBottomId.value > 2) {
    SwitchDecideBottomId.value = 0;
  }
  SwitchDecideBottomId.value++;
  GeneratedBondingCodeDetail.loading = true;
  getGeneratedBondingCodeDetail(props.activeClickstyleDocTreeIdArr[0]).then((res) => {
    GeneratedBondingCodeDetail.data = SwitchDecideBottomFn(res.data);
    GeneratedBondingCodeDetail.loading = false;
    const filtersFields = [
      'sessionType',
      'styleCode',
      'customStyleCode',
      'orderStyleColorName',
      'orderStyleSizeName',
      'sizeCount',
      'deptName',
      'taskUserName',
      'packetCount',
      'assignQuantity',
      'totalCount'
    ];
    proxy.filtersFieldsFn(filtersFields, GeneratedBondingCodeDetail);

    setTimeout(() => {
      GeneratedBondingCodeDetailKey.value = Math.random();
    }, 100);
  });
};
const SwitchDecideBottomFn = (data) => {
  let filterData = [];
  if (SwitchDecideBottomId.value == 1) {
    filterData = data.filter((item) => item.printCount == 0);
  } else if (SwitchDecideBottomId.value == 2) {
    filterData = data.filter((item) => item.printCount >= 1);
  } else {
    filterData = data;
  }
  return filterData;
};
</script>
<style lang='scss' scoped>
.automaticHeight {
  height: calc(100vh - 130px);
  //   background-color: #cfc;
  overflow: hidden;
  .content {
    height: 100%;
    overflow-y: auto;
  }
}
:deep(.el-dialog__header) {
  padding: 10px !important;
}
.flip-container {
  perspective: 1000px;
  height: calc(100% - 40px);
  width: 100%;

  &.flip .flipper {
    transform: rotateY(180deg);
  }
}

.flipper {
  transition: 0.6s;
  transform-style: preserve-3d;
  position: relative;
  height: 100%;
}

.front,
.back {
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.front {
  z-index: 2;
  transform: rotateY(0deg);
}

.back {
  transform: rotateY(180deg);
  background: #fff;
}

.code-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
}
::v-deep(.mylist-table-outsole.vxe-grid .vxe-header--column.col-blue) {
  color: #409eff;
}
</style>
