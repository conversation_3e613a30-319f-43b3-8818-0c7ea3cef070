<template>
  <div>
    <el-dropdown class="dr-box" style="margin-left: 0px" @command="examine">
      <el-button>
        <el-icon> <Avatar /> </el-icon>交付<el-icon class="el-icon--right"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item  command="1">裁断</el-dropdown-item>
          <el-dropdown-item  command="2">针车</el-dropdown-item>
          <el-dropdown-item  command="3">打底</el-dropdown-item>
          <el-dropdown-item  command="4">加工</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>
<script  setup>
import { reactive } from 'vue';

const examine=(val)=>{
  console.log(val)
}
</script>
<style lang='scss' scoped>
</style>
