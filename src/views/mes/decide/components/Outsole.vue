<template>
  <div>
    <el-dialog v-model="OutsoleDialogVisible" :fullscreen="true" class="check-dialog" :before-close="closeOutsole">
      <div class="automaticHeight">
        <div class="content">
          <div style="display: flex; justify-content: right; align-items: center">
            <!-- <span style="margin-right: 10px;">计划完成时间：<span style="color: #409EFF">{{ props?.OutsoleList[0].shipmentDate }}</span></span> -->
            <span style="margin-right: 10px">每箱双数：<el-input v-model="sys_cofig_box_numberValue" style="width: 100px" /></span>
            <!-- <span style="margin-right: 10px;"><el-button type="primary">生成箱数</el-button></span> -->
            <span
              ><el-button type="primary" @click="toggleView">{{ isShowCode ? '返回列表' : '查看箱码列表' }}</el-button></span
            >
          </div>
          <div class="flip-container" :class="{ 'flip': isShowCode }">
            <div class="flipper">
              <div class="front">
                <vxe-grid class="mylist-table-outsole" ref="outsoleRef" v-bind="Outsole">
                  <template #default_user="{ row, rowIndex }">
                    <el-button v-if="!row.taskUserId" type="primary" link @click="openUserList(row)">选择</el-button>
                    <span v-else
                      >{{ row.taskUserName }}
                      <el-button type="primary" link @click="openUserList(row)">重选</el-button>
                      <el-button link @click="synchronizationUser(row, rowIndex)"
                        ><el-icon color="#409EFF"><Switch /></el-icon></el-button
                    ></span>
                  </template>
                  <template #edit_assignQuantityt="{ row, column }">
                    <!-- <el-input v-model="row[column.property]" /> -->
                    <el-input-number v-model="row[column.property]" :disabled="row.isGeneratedComponentCode == 1" />
                  </template>
                </vxe-grid>
              </div>
              <div class="back">
                <vxe-grid class="mylist-table-outsole" ref="outsole2Ref" v-bind="GeneratedBondingCodeDetail">
                  <template #default_qrCodeImage="{ row, column }">
                    <span
                      >{{ row[column.property]
                      }}<el-button
                        :type="row.type ? 'success' : 'primary'"
                        link
                        @click="
                          openQrCodeImage(row.bondingQrCode);
                          row.type = 'success';
                        "
                        >打开</el-button
                      ></span
                    >
                  </template>
                </vxe-grid>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handlePrint">打印条码</el-button>
          <el-button @click="closeOutsole">关闭</el-button>
          <el-button type="primary" @click="generateBoxes">生成箱数</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="userListDialog" title="大底用户列表" width="500">
      <span
        ><el-table :data="userListData" border height="500px" @row-dblclick="userListDataDblclick">
          <el-table-column property="userName" label="用户昵称" />
          <el-table-column property="deptName" label="部门" /> </el-table
      ></span>
    </el-dialog>
    <el-dialog v-model="QrCodeImagedialogVisible" title="二维码" width="500" :close-on-click-modal="true">
      <span><img :src="qrCodeImage" alt="QR Code" /></span>
    </el-dialog>
  </div>
</template>
<script  setup>
import QRCode from 'qrcode';
import { sys_cofig_box_number, creatGeneratedBondingCode, getGeneratedBondingCodeDetail, getUserListByTaskName,printBondingCode } from '@/api/mes/Outsole';
import { reactive } from 'vue';
const { proxy } = getCurrentInstance();
const emits = defineEmits(['closeOutsole']);
const props = defineProps({
  OutsoleList: {},
  activeClickstyleDocTreeIdArr: {},
  taskConfigurationId: {}
});

const OutsoleDialogVisible = ref(true);
const inputFilterRender = reactive({
  name: 'VxeInput'
});

const Outsole = reactive({
  headerCellClassName({ column }) {
    if (column.field === 'assignQuantity') {
      return 'col-blue';
    }
    return null;
  },
  id: 'TaskListOutsole',
  border: true,
  height: '100%',
  rowConfig: {
    useKey: true
  },
  tooltipConfig: {
    showAll: true,
    enterable: true,
    contentMethod: ({ type, column, row, items, _columnIndex }) => {
      return null;
    }
  },
  showOverflow: true,
  keepSource: true,
  resizableConfig: {
    isDblclickAutoWidth: true,
    minWidth: true
  },
  columnConfig: {
    useKey: true,
    resizable: true
  },
  customConfig: {
    mode: 'modal',
    storage: true,
    modalOptions: {
      height: '500px',
      className: 'decideRight-modal'
    }
  },
  toolbarConfig: {
    custom: true
  },
  scrollY: {
    enabled: true,
    gt: 0
  },
  scrollX: {
    enabled: true,
    gt: 0
  },
  loading: false,
  editConfig: {
    trigger: 'click',
    mode: 'row'
  },
  columns: [
    { type: 'checkbox', width: 60 },
    { type: 'seq', width: 70 },
    {
      width: '150',
      title: '季节号',
      field: 'sessionType',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '115',
      title: '款式编号',
      field: 'styleCode',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '130',
      title: '制令号',
      field: 'customStyleCode',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '颜色',
      field: 'orderStyleColorName',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '尺码',
      field: 'orderStyleSizeName',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '订单数量',
      field: 'sizeCount',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '派工数',
      field: 'assignQuantity',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender,
      // editRender: { name: 'VxeInput', props: { clearable: true } },
      editRender: { autofocus: '.el-input__inner' },
      slots: { edit: 'edit_assignQuantityt' }
    },
    {
      width: '150',
      title: '线别',
      field: 'deptName',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '负责人',
      field: 'taskUserName',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender,
      slots: { default: 'default_user' }
    }
  ],
  data: []
});
const GeneratedBondingCodeDetail = reactive({
  id: 'GeneratedBondingCodeDetail',
  border: true,
  height: '100%',
  rowConfig: {
    useKey: true
  },
  tooltipConfig: {
    showAll: true,
    enterable: true,
    contentMethod: ({ type, column, row, items, _columnIndex }) => {
      return null;
    }
  },
  showOverflow: true,
  keepSource: true,
  resizableConfig: {
    isDblclickAutoWidth: true,
    minWidth: true
  },
  columnConfig: {
    useKey: true,
    resizable: true
  },
  customConfig: {
    mode: 'modal',
    storage: true,
    modalOptions: {
      height: '500px',
      className: 'decideRight-modal'
    }
  },
  toolbarConfig: {
    custom: true
  },
  scrollY: {
    enabled: true,
    gt: 0
  },
  scrollX: {
    enabled: true,
    gt: 0
  },
  loading: false,
  editConfig: {
    trigger: 'click',
    mode: 'row'
  },
  columns: [
    {
      field: 'checkbox',
      type: 'checkbox',
      width: 60,
      fixed: 'left',
      align: 'center'
    },
    { type: 'seq', width: 70 },
    {
      width: '150',
      title: '季节号',
      field: 'sessionType',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '115',
      title: '款式编号',
      field: 'styleCode',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '130',
      title: '制令号',
      field: 'customStyleCode',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '颜色',
      field: 'orderStyleColorName',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '尺码',
      field: 'orderStyleSizeName',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '订单数量',
      field: 'totalCount',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender
    },
    {
      width: '150',
      title: '派工数',
      field: 'assignQuantity',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender,
      editRender: { name: 'VxeInput', props: { clearable: true } }
    },
    {
      width: '150',
      title: '线别',
      field: 'deptName',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender,
      editRender: { name: 'VxeInput', props: { clearable: true } }
    },
    {
      width: '150',
      title: '负责人',
      field: 'taskUserName',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender,
      editRender: { name: 'VxeInput', props: { clearable: true } }
    },
    {
      width: '250',
      title: '箱码',
      field: 'bondingQrCode',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: inputFilterRender,
      slots: { default: 'default_qrCodeImage' }
    }
  ],
  data: []
});
watch(
  () => props.OutsoleList,
  (newVal) => {
    if (newVal) {
      Outsole.data = newVal;
      setTimeout(() => {
        const $table = outsoleRef.value;
        let rows = Outsole.data.filter((item) => item.isGeneratedComponentCode == 1);
        if ($table) {
          $table.setCheckboxRow(rows, true);
        }
      }, 300);
    }
  },
  { deep: true, immediate: true }
);
const closeOutsole = () => {
  console.log(1);
  OutsoleDialogVisible.value = false;
  emits('closeOutsole');
};
const isShowCode = ref(false);

const toggleView = () => {
  isShowCode.value = !isShowCode.value;
  if (isShowCode.value) {
    getGeneratedBondingCodeDetailFN();
  }
};

onMounted(() => {
  sys_cofig_box_numberFn();
});
let sys_cofig_box_numberValue = ref('');

const sys_cofig_box_numberFn = () => {
  sys_cofig_box_number().then((res) => {
    sys_cofig_box_numberValue.value = res.data;
  });
};

const outsoleRef = ref(null);
//生成箱数
const generateBoxes = () => {
  console.log(props.taskConfigurationId.mesTaskConfigurationId);
  const $grid = outsoleRef.value;
  const selectRecords = $grid.getCheckboxRecords();
  selectRecords.map((item) => {
    item.mesTaskConfigurationId = props.taskConfigurationId.mesTaskConfigurationId;
  });
  if (selectRecords.length === 0) {
    proxy?.$modal.msgWarning('请勾选需要生成箱数的行');
    return;
  }
  selectRecords.map((item) => {
    item.packetNumber = Number(sys_cofig_box_numberValue.value);
  });
  creatGeneratedBondingCode(selectRecords).then((res) => {
    proxy?.$modal.msgSuccess('生成成功');
    toggleView();
  });
};
const getGeneratedBondingCodeDetailFN = (id) => {
  getGeneratedBondingCodeDetail(props.activeClickstyleDocTreeIdArr[0]).then((res) => {
    GeneratedBondingCodeDetail.data = res.data;
  });
};
const userListDialog = ref(false);
const userListData = ref([]);
let activeDecideRightRow;
const openUserList = (row) => {
  activeDecideRightRow = row;
  userListDialog.value = true;
  getUserListByTaskName(props.taskConfigurationId.mesTaskConfigurationId).then((res) => {
    userListData.value = res.data;
  });
};
const userListDataDblclick = (row) => {
  activeDecideRightRow.taskUserName = row.userName;
  activeDecideRightRow.taskUserId = row.userId;
  activeDecideRightRow.deptId = row.deptId;
  activeDecideRightRow.deptName = row.deptName;
  userListDialog.value = false;
  console.log(row);
};
const synchronizationUser = (row, index) => {
  console.log(row, index);
  //切割Outsole.data 从index开始的数组
  Outsole.data.slice(index).map((item) => {
    console.log(item);
    item.taskUserName = row.taskUserName;
    item.taskUserId = row.taskUserId;
    item.deptId = row.deptId;
    item.deptName = row.deptName;
  });
  // console.log(Outsole.data);
};
const qrCodeImage = ref();
const QrCodeImagedialogVisible = ref(false);
const openQrCodeImage = (val) => {
  QRCode.toDataURL(val, (err, url) => {
    console.log(url);
    QrCodeImagedialogVisible.value = true;
    qrCodeImage.value = url;
  });
};
const outsole2Ref = ref(null);
const handlePrint = () => {
  // const $grid = outsole2Ref.value;
  // const selectRecords = $grid.getCheckboxRecords();
  // if (selectRecords.length === 0) {
  //   proxy?.$modal.msgWarning('请勾选需要打印的数据');
  //   isShowCode.value = true;
  //   if(isShowCode.value){

  //     getGeneratedBondingCodeDetailFN();
  //   }
  //   return;
  // }
  console.log(isShowCode.value);
  if (!isShowCode.value) {
    isShowCode.value = true;
    getGeneratedBondingCodeDetailFN();
  } else {
    const $grid = outsole2Ref.value;
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length === 0) {
      proxy?.$modal.msgWarning('请勾选需要打印的数据');
      return
    }
    printBondingCode(selectRecords).then((res) => {
      proxy?.$modal.msgSuccess('打印成功');
    });
  }
};
</script>
<style lang='scss' scoped>
.automaticHeight {
  height: calc(100vh - 104px);
  //   background-color: #cfc;
  overflow: hidden;
  .content {
    height: 100%;
    overflow-y: auto;
  }
}
:deep(.el-dialog__header) {
  padding: 10px !important;
}
.flip-container {
  perspective: 1000px;
  height: calc(100% - 40px);
  width: 100%;

  &.flip .flipper {
    transform: rotateY(180deg);
  }
}

.flipper {
  transition: 0.6s;
  transform-style: preserve-3d;
  position: relative;
  height: 100%;
}

.front,
.back {
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.front {
  z-index: 2;
  transform: rotateY(0deg);
}

.back {
  transform: rotateY(180deg);
  background: #fff;
}

.code-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
}
::v-deep(.mylist-table-outsole.vxe-grid .vxe-header--column.col-blue) {
  color: #409eff;
}
</style>
