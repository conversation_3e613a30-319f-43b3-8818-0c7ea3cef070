<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div>
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4">
        <!-- <div>{{cardOrTableArr.length}}</div> -->
        <div
          v-for="item in cardOrTableArr"
          :key="item.id"
          class="overflow-hidden rounded-lg bg-white shadow"
          :class="isShowBorder(item.mesOrderStyleStitchingListId)"
          @click="cardClick(item)"
        >
          <div class="p-4">
            <div class="flex gap-4">
              <div class="h-24 w-24 flex-shrink-0">
                <el-image
                  :src="item.mesOrderStyle?.styleImageBase64"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="[item.mesOrderStyle?.styleImageBase64]"
                  show-progress
                  :initial-index="4"
                  :hide-on-click-modal="true"
                  fit="contain"
                  class="h-full w-full rounded object-cover"
                />
              </div>
              <div class="flex-1" style="min-width: 0">
                <div class="space-y-1 text-sm text-gray-500" style="width: 100%; max-width: 100%; overflow: hidden">
                  <el-tooltip :content="'品牌:' + item.mesOrderStyle?.customName" placement="bottom">
                    <div class="no-wrap-text" style="width: 100%; max-width: 100%; display: block; overflow: hidden; text-overflow: ellipsis">
                      品牌：{{ item.mesOrderStyle?.customName }}
                    </div>
                  </el-tooltip>
                  <el-tooltip :content="'制令号：' + item?.customStyleCode" placement="bottom">
                    <div class="no-wrap-text" style="width: 100%; max-width: 100%; display: block; overflow: hidden; text-overflow: ellipsis">
                      制令号：{{ item?.customStyleCode }}
                    </div>
                  </el-tooltip>
                  <div class="no-wrap-text" style="width: 100%; max-width: 100%; display: block; overflow: hidden; text-overflow: ellipsis">
                    季节：{{ item.mesOrderStyle?.sessionType }}
                  </div>
                  <div class="no-wrap-text" style="width: 100%; max-width: 100%; display: block; overflow: hidden; text-overflow: ellipsis">
                    订单数量：{{ item?.totalCount }}
                  </div>
                  <el-tooltip :content="'客户交期：' + item?.shipmentDate" placement="bottom">
                    <div class="no-wrap-text" style="width: 100%; max-width: 100%; display: block; overflow: hidden; text-overflow: ellipsis">
                      客户交期：{{ formatDate(item?.shipmentDate) }}
                    </div>
                  </el-tooltip>
                  <el-tooltip :content="'针车上线日：' + item?.shipmentDate" placement="bottom">
                    <div class="no-wrap-text" style="width: 100%; max-width: 100%; display: block; overflow: hidden; text-overflow: ellipsis">
                      针车上线日：{{ formatDate(item?.startTime) }}
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>

            <div class="mt-4 flex items-center">
              <div class="relative flex w-full">
                <div v-for="(step, index) in item.mesTaskAssignList" :key="index" class="flex-1">
                  <div class="relative flex items-center justify-center">
                    <div
                      style="z-index: 99"
                      :class="[
                        'h-8 w-8 rounded-full border-2 flex items-center justify-center text-sm',
                        step.isComplete == 1
                          ? 'border-blue-500 bg-blue-500 text-white'
                          : step.isComplete == 2
                          ? 'border-green-500 bg-green-500 text-white'
                          : new Date() > new Date(item.startTime) && step.isComplete === 0
                          ? 'border-red-500 bg-red-500 text-white'
                          : 'border-gray-300 bg-white text-gray-500'
                      ]"
                      @click.stop="openFaProcessed(index + 1, item)"
                    >
                      {{ index + 1 }}
                    </div>
                    <div
                      v-if="index < item.mesTaskAssignList.length - 1"
                      :class="['absolute h-0.5 w-full left-1/2', step.isComplete == 1 ? 'bg-blue-500' : 'bg-gray-300']"
                    ></div>
                  </div>
                  <div class="mt-2 text-center">
                    <div class="text-base font-medium" :class="step.isComplete == 1 ? 'text-blue-500' : 'text-gray-500'">
                      {{ step.taskName }}
                    </div>
                    <div class="mt-1 text-sm text-gray-400">
                      {{ step.taskUserName }}
                    </div>
                    <div class="text-xs text-gray-400">
                      {{ step.startTime }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="position: absolute; right: 10px; bottom: 5px" v-if="isSelect(item.mesOrderStyleStitchingListId)">
              <el-icon color="#409EFF"><Select /></el-icon>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="mes_loading">
        <el-icon class="is-loading" v-if="loadingIsShow"><Loading /></el-icon>
        <span v-else>全部加载完成</span>
      </div> -->
    </div>
  </div>
</template>

  <script  setup>
import { ref, computed } from 'vue';
import { Search, Plus } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { getTaskConfigurationListByRoleIds } from '@/api/mes/decide';
const props = defineProps({
  cardOrTableArr: {},
  LoadMoreLoadingIsShow: {},
  NewactiveClickArr1: {},
  CurrentlyCheckedId: {},
  activeClickstyleDocTreeIdArr: {}
});

const emits = defineEmits(['cardFn', 'LoadMore', 'loadingComplete', 'openProcessed']);

const isShowBorder = (id) => {
  return activeClickArr.value.includes(id) ? 'Select_card' : '';
};
const isSelect = (id) => {
  return activeClickArr.value.includes(id);
};
function formatDate(dateString) {
  return dateString ? dateString.split(' ')[0] : '';
}
const activeClickArr = ref([]);
const activeClickstyleDocTreeIdArr = ref([]);
const NewactiveClickArr = ref([]);
const cardClick = (item) => {
  const { mesOrderStyleStitchingListId, styleDocTreeId } = item;
  const index = activeClickArr.value.indexOf(mesOrderStyleStitchingListId);
  // const index2 = activeClickstyleDocTreeIdArr.value.indexOf(styleDocTreeId);
  if (activeClickArr.value.includes(mesOrderStyleStitchingListId)) {
    activeClickArr.value.splice(index, 1);
    activeClickstyleDocTreeIdArr.value.splice(index, 1);
    NewactiveClickArr.value.splice(index, 1);
  } else {
    activeClickArr.value.push(mesOrderStyleStitchingListId);
    activeClickstyleDocTreeIdArr.value.push(styleDocTreeId);
    NewactiveClickArr.value.push(item);
  }
  emits('cardFn', activeClickArr.value, activeClickstyleDocTreeIdArr.value, NewactiveClickArr.value);
};
// 定义一个全局的isLoading变量
const isLoading = ref(false);
const loadingIsShow = ref(props.LoadMoreLoadingIsShow);
// const loadingIsShow = ref(true);
// const handleLoadingComplete = (val) => {
const loadingComplete = (val) => {
  isLoading.value = false;
  loadingIsShow.value = val;
};
const clearBorder = () => {
  activeClickArr.value = [];
};
defineExpose({
  loadingComplete,
  clearBorder
  // loadingComplete: handleLoadingComplete,
  // clearBorder:clearBorderFn
});

onMounted(() => {});
const openFaProcessed = (number, item) => {
  emits('openProcessed', number, item);
};
watch(
  () => props,
  (newVal) => {
    if (newVal) {
      // console.log(props);
      NewactiveClickArr.value = props.NewactiveClickArr1;
      activeClickArr.value = props.CurrentlyCheckedId;
      activeClickstyleDocTreeIdArr.value = props.activeClickstyleDocTreeIdArr;
    }
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  NewactiveClickArr.value = props.NewactiveClickArr1;
  activeClickArr.value = props.CurrentlyCheckedId;
  activeClickstyleDocTreeIdArr.value = props.activeClickstyleDocTreeIdArr;
});
const colorFn = (index, step) => {
  // step.isComplete == 1
  //   ? 'border-blue-500 bg-blue-500 text-white'
  //   : step.isComplete == 2
  //   ? 'border-green-500 bg-green-500 text-white'
  //   : step.overdue == 1
  //   ? 'border-red-500 bg-red-500 text-white'
  //   : 'border-gray-300 bg-white text-gray-500';
  // if (index == 0) {
  //   return step.isComplete == 1
  //     ? 'border-blue-500 bg-blue-500 text-white'
  //     : step.isComplete == 2
  //     ? 'border-green-500 bg-green-500 text-white'
  //     : step.overdue == 1
  //     ? 'border-red-500 bg-red-500 text-white'
  //     : 'border-gray-300 bg-white text-gray-500';
  // }else if(index == 1){
  // }
};
</script>

<style scoped lang='scss'>
.min-h-screen {
  min-height: calc(100vh - 234px);
  max-height: calc(100vh - 234px);
  overflow: hidden;
  overflow-y: auto;
}
.py-8 {
  padding: 10px 10px 10px 0px;
}
.Select_card {
  border: 2px solid #409eff;
}
// .no-wrap-text {
//   white-space: nowrap; /* 防止内容换行 */
//   overflow: hidden; /* 隐藏超出容器宽度的内容 */
//   text-overflow: ellipsis; /* 当文本溢出时显示省略号，可选 */
//   width: 100%; /* 设置一个具体的宽度或百分比，以确定何时开始裁剪内容 */
// }
.no-wrap-text {
  white-space: nowrap; /* 防止内容换行 */
  overflow: hidden; /* 隐藏超出容器宽度的内容 */
  text-overflow: ellipsis; /* 当文本溢出时显示省略号，可选 */
  width: 100%; /* 设置一个具体的宽度或百分比，以确定何时开始裁剪内容 */
}
.overflow-hidden {
  position: relative;
}
.mes_loading {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

