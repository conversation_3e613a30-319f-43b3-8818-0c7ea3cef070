<template>
  <div>
    <el-dialog v-model="processedDialogIsShow" title="已处理" :fullscreen="true" :before-close="handleClose">
      <div v-if="openprocessedNumber == 1">
        <div class="query_box">
          <el-form :model="queryParams" ref="queryFormRef">
            <el-row :gutter="10">
              <!-- 制令号输入 -->
              <el-col :xl="5" :lg="5" :md="4" :sm="4" :xs="24" style="margin-bottom: 5px">
                <el-form-item prop="customStyleCode">
                  <el-input v-model="queryParams.customStyleCode" placeholder="请输入制令号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :xl="5" :lg="5" :md="4" :sm="4" :xs="24" style="margin-bottom: 5px">
                <el-form-item prop="styleCode">
                  <el-input v-model="queryParams.styleCode" placeholder="请输入款式编号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-col>

              <el-col :xl="2" :lg="2" :md="2" :sm="2" :xs="2" style="margin-bottom: 5px">
                <el-form-item prop="styleCode">
                  <el-button type="primary" @click="handleQuery">搜索</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="table23">
          <vxe-grid
            class="mylist-table2"
            ref="decideProcessedRef"
            v-bind="decideProcessed"
            :key="decideProcessedKey"
            height="100%"
            empty-text="没有数据!"
          >
            <template #default_qrCodeImage="{ row, column }">
              <span
                >{{ row[column.property]
                }}<el-button
                  :type="row.type ? 'success' : 'primary'"
                  link
                  @click="
                    openQrCodeImage(row.partQrCode);
                    row.type = 'success';
                  "
                  >打开</el-button
                ></span
              >
            </template>
            <template #default_Progress="{ row, column }">
              <el-progress :text-inside="true" :stroke-width="26" :percentage="row[column.property]" status="success" />
            </template>
          </vxe-grid>
        </div>
      </div>
      <div v-if="openprocessedNumber == 2">
        <div class="query_box">
          <el-form :model="queryParams" ref="queryFormRef">
            <el-row :gutter="10">
              <el-col :xl="5" :lg="5" :md="4" :sm="4" :xs="24" style="margin-bottom: 5px">
                <el-form-item prop="customStyleCode">
                  <el-input v-model="queryParams2.customStyleCode" placeholder="请输入制令号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :xl="5" :lg="5" :md="4" :sm="4" :xs="24" style="margin-bottom: 5px">
                <el-form-item prop="styleCode">
                  <el-input v-model="queryParams2.styleCode" placeholder="请输入款式编号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :xl="2" :lg="2" :md="2" :sm="2" :xs="2" style="margin-bottom: 5px">
                <el-form-item prop="styleCode">
                  <el-button type="primary" @click="handleQuery2">搜索</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="table23">
          <vxe-grid
            class="mylist-table2"
            ref="decideProcessedRef"
            v-bind="decideProcessed2"
            :key="decideProcessed2Key"
            height="100%"
            empty-text="没有数据!"
          >
            <template #default_qrCodeImage="{ row, column }">
              <span
                >{{ row[column.property]
                }}<el-button
                  :type="row.type ? 'success' : 'primary'"
                  link
                  @click="
                    openQrCodeImage(row.partQrCode);
                    row.type = 'success';
                  "
                  >打开</el-button
                ></span
              >
            </template>
            <template #default_Progress="{ row, column }">
              <el-progress :text-inside="true" :stroke-width="26" :percentage="row[column.property]" status="success" />
            </template>
          </vxe-grid>
        </div>
      </div>
      <div v-if="openprocessedNumber == 3">
        <div class="query_box">
          <el-form :model="queryParams" ref="queryFormRef">
            <el-row :gutter="10">
              <!-- 制令号输入 -->
              <el-col :xl="5" :lg="5" :md="4" :sm="4" :xs="24" style="margin-bottom: 5px">
                <el-form-item prop="customStyleCode">
                  <el-input v-model="queryParams3.customStyleCode" placeholder="请输入制令号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :xl="5" :lg="5" :md="4" :sm="4" :xs="24" style="margin-bottom: 5px">
                <el-form-item prop="styleCode">
                  <el-input v-model="queryParams3.styleCode" placeholder="请输入款式编号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-col>

              <el-col :xl="2" :lg="2" :md="2" :sm="2" :xs="2" style="margin-bottom: 5px">
                <el-form-item prop="styleCode">
                  <el-button type="primary" @click="handleQuery3">搜索</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="table23">
          <vxe-grid class="mylist-table-processedOutsole" ref="processedOutsoleRef" v-bind="processedOutsole" :key="processedOutsoleKey">
            <template #default_qrCodeImage="{ row, column }">
              <span
                >{{ row[column.property]
                }}<el-button
                  :type="row.type ? 'success' : 'primary'"
                  link
                  @click="
                    openQrCodeImage(row[column.property]);
                    row.type = 'success';
                  "
                  >打开</el-button
                ></span
              >
            </template>
            <template #default_Progress="{ row, column }">
              <el-progress :text-inside="true" :stroke-width="26" :percentage="row[column.property]" status="success" />
            </template>
          </vxe-grid>
        </div>
      </div>
      <div v-if="openprocessedNumber == 4">
        <div class="query_box">
          <el-form :model="queryParams" ref="queryFormRef">
            <el-row :gutter="10">
              <!-- 制令号输入 -->
              <el-col :xl="5" :lg="5" :md="4" :sm="4" :xs="24" style="margin-bottom: 5px">
                <el-form-item prop="customStyleCode">
                  <el-input v-model="queryParams4.customStyleCode" placeholder="请输入制令号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :xl="5" :lg="5" :md="4" :sm="4" :xs="24" style="margin-bottom: 5px">
                <el-form-item prop="styleCode">
                  <el-input v-model="queryParams4.styleCode" placeholder="请输入款式编号" style="width: 100%" clearable></el-input>
                </el-form-item>
              </el-col>

              <el-col :xl="2" :lg="2" :md="2" :sm="2" :xs="2" style="margin-bottom: 5px">
                <el-form-item prop="styleCode">
                  <el-button type="primary" @click="handleQuery4">搜索</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="table23">
          <vxe-grid class="mylist-table-processedProcessing" ref="processedProcessingRef" v-bind="processedProcessing" :key="processedProcessingKey">
            <template #default_Progress="{ row, column }">
              <el-progress :text-inside="true" :stroke-width="26" :percentage="row[column.property]" status="success" />
            </template>
          </vxe-grid>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="QrCodeImagedialogVisible" title="二维码" width="500" :close-on-click-modal="true">
      <span><img :src="qrCodeImage" alt="QR Code" /></span>
    </el-dialog>
  </div>
</template>
<script  setup>
import { reactive } from 'vue';
import QRCode from 'qrcode';
import {
  getMesStockCuttingInDetailByParam,
  getTaskAssignSewingListByParam,
  getStockBondingInListByParam,
  getStockProcessInListByParam,
  getMesTaskAssignCuttingCodeListByParam
} from '@/api/mes/decide';
import { index } from '../index';
const { proxy } = getCurrentInstance();
const { decideProcessed, decideProcessed2, processedOutsole, processedProcessing } = index();

const props = defineProps({
  processedComponent: {}, //已处理模块
  processedIsShow: {},
  openprocessedNumber: {},
  openprocesseItem: {}
});
const emits = defineEmits(['closeProcessedDialog']);
const queryParams = ref({ pageNum: 1, pageSize: 100, customStyleCode: props.openprocesseItem.customStyleCode });
const queryParams2 = ref({ pageNum: 1, pageSize: 100, customStyleCode: props.openprocesseItem.customStyleCode });
const queryParams3 = ref({ pageNum: 1, pageSize: 100, customStyleCode: props.openprocesseItem.customStyleCode });
const decideProcessedKey = ref(Math.random());
const handleQuery = () => {
  console.log(queryParams.value);
  getMesTaskAssignCuttingCodeListByParam(queryParams.value).then((res) => {
    // console.log(res);

    decideProcessed.data = res.data.records;
    decideProcessed.data.forEach((item) => {
      item.lv = ((item.assignQuantity / item.sizeOrderCount) * 100).toFixed(2);
      // console.log(item.assignQuantity, item.sizeOrderCount);
      // console.log(item.lv);
    });
    console.log(decideProcessed.data);
    const filtersFields = [
      'orderStyleSizeName',
      'assignQuantity',
      'storageQuantity',
      'partQrCode',
      'orderStyleColorName',
      'partName',
      'units',
      'taskUserName',
      'sessionType',
      'styleCode',
      'customStyleCode',
      'packetCount',
      'lv'
    ];
    proxy.filtersFieldsFn(filtersFields, decideProcessed);
    decideProcessedKey.value = Math.random();
  });
  // getMesStockCuttingInDetailByParam(queryParams.value).then((res) => {
  //   console.log(res);
  //   decideProcessed.data = res.data.records;
  // });
};
const qrCodeImage = ref();
const QrCodeImagedialogVisible = ref(false);
const openQrCodeImage = (val) => {
  console.log(val);
  QRCode.toDataURL(val, (err, url) => {
    console.log(url);
    QrCodeImagedialogVisible.value = true;
    qrCodeImage.value = url;
  });
};
const decideProcessed2Key = ref(Math.random());
const handleQuery2 = () => {
  console.log(queryParams.value);
  getTaskAssignSewingListByParam(queryParams2.value).then((res) => {
    console.log(res);
    decideProcessed2.data = res.data;
    decideProcessed2.data.map((item) => {
      item.t1 = ((item.assignQuantity / item.sizeOrderCount) * 100).toFixed(2);
      item.t2 = ((item.totalCompletCount / item.assignQuantity) * 100).toFixed(2);
    });
    const filtersFields = [
      'orderStyleSizeName',
      'assignQuantity',
      'sizeOrderCount',
      'totalCompletCount',
      'orderStyleColorName',
      'styleCode',
      'customStyleCode',
      't1',
      't2'
    ];
    proxy.filtersFieldsFn(filtersFields, decideProcessed2);
    decideProcessed2Key.value = Math.random();
  });
};
const processedOutsoleKey = ref(Math.random());
const handleQuery3 = () => {
  // console.log(queryParams.value);
  getStockBondingInListByParam(queryParams3.value).then((res) => {
    console.log(res);
    processedOutsole.data = res.data;
    processedOutsole.data.map((item) => {
      item.t1 = ((item.completedQuantity / item.sizeOrderCount) * 100).toFixed(2);
    });
    const filtersFields = ['orderStyleSizeName', 'completedQuantity', 'sizeOrderCount', 'orderStyleColorName', 'styleCode', 'customStyleCode', 't1'];
    proxy.filtersFieldsFn(filtersFields, processedOutsole);
    processedOutsoleKey.value = Math.random();
  });
};
const queryParams4 = ref({ pageNum: 1, pageSize: 100, customStyleCode: props.openprocesseItem.customStyleCode });
const processedProcessingKey = ref(Math.random());
const handleQuery4 = () => {
  getStockProcessInListByParam(queryParams4.value).then((res) => {
    // console.log(res);
    processedProcessing.data = res.data;
    processedProcessing.data.map((item) => {
      item.t1 = ((item.completedQuantity / item.sizeOrderCount) * 100).toFixed(2);
      item.t2 = ((item.totalCompletCount / item.sizeOrderCount) * 100).toFixed(2);
    });
    const filtersFields = [
      'orderStyleSizeName',
      'completedQuantity',
      'sizeOrderCount',
      'totalCompletCount',
      'orderStyleColorName',
      'styleCode',
      'customStyleCode',
      't1',
      't2'
    ];
    proxy.filtersFieldsFn(filtersFields, processedProcessing);
    processedProcessingKey.value = Math.random();
  });
};
const processedDialogIsShow = ref(true);
const handleClose = () => {
  emits('closeProcessedDialog', false);
};
watch(
  () => props.openprocessedNumber,
  (newValue, oldValue) => {
    if (newValue == 1) {
      handleQuery();
    } else if (newValue == 2) {
      handleQuery2();
    } else if (newValue == 3) {
      handleQuery3();
    } else if (newValue == 4) {
      handleQuery4();
    }
  },
  {
    immediate: true // 立即执行一次回调函数
  }
);
</script>
<style lang='scss' scoped>
.min-h-screen {
  min-height: calc(100vh - 184px);
  max-height: calc(100vh - 184px);
  overflow: hidden;
  overflow-y: auto;
  //   background-color: aqua;
}
.py-8 {
  padding: 10px 0px !important;
}
::v-deep(.decideRight-modal) {
  div {
    top: 0px !important;
  }
}
.table23 {
  height: calc(100vh - 174px);
  // background-color: #cfc;
}
</style>
