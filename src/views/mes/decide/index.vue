<template>
  <div class="decide_box" v-loading="showMask">
    <div class="query_box">
      <el-form :model="queryParams" ref="queryFormRef">
        <el-row :gutter="20">
          <!-- 进度选择 -->
          <el-col :xl="6" :lg="6" :md="6" :sm="6" :xs="24" style="margin-bottom: 5px">
            <el-form-item prop="customStyleCode">
              <el-select v-model="queryParams.customStyleCode" placeholder="请选择制令号" style="width: 100%" clearable>
                <el-option
                  v-for="item in scheduleOptions"
                  :key="item.customStyleCode"
                  :label="item.customStyleCode"
                  :value="item.customStyleCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 关键词输入 -->
          <el-col :xl="6" :lg="6" :md="6" :sm="6" :xs="24" style="margin-bottom: 5px">
            <el-form-item prop="keyWord">
              <el-input v-model="queryParams.keyWord" placeholder="请输入关键词" style="width: 100%" clearable></el-input>
            </el-form-item>
          </el-col>

          <!-- 日期范围选择 -->
          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24" style="margin-bottom: 5px">
            <el-form-item prop="startEndTime">
              <el-date-picker
                v-model="queryParams.startEndTime"
                type="daterange"
                unlink-panels
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                :shortcuts="shortcuts"
                style="width: 100%"
                clearable
                @focus="(e) => e.target.blur()"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="button">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="success" plain icon="Search" @click="getOrderStyleStitchingListFn">查询</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Close" @click="resetQuery">重置</el-button>
        </el-col>

        <el-col :span="1.5" style="display: flex; align-items: center">
          <el-button
            :type="mode == 1 ? 'primary' : ''"
            @click="
              mode = 1;
              activeName.name = card;
            "
            >卡片模式</el-button
          >
        </el-col>
        <el-col :span="1.5" style="display: flex; align-items: center">
          <el-button
            :type="mode == 2 ? 'primary' : ''"
            @click="
              mode = 2;
              activeName.name = list;
            "
            >列表模式</el-button
          >
        </el-col>
        <el-col :span="1.5" style="margin-left: auto">
          <el-button type="primary" @click="goDistributionSheet2">打印分配单</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button plain @click="opendesignate('1905509608912551938')" :disabled="CurrentlyCheckedId.length == 0">裁断派工</el-button>
        </el-col>
      </el-row>
    </div>
    <component
      ref="dynamicComponent"
      :is="activeName.name"
      :cardOrTableArr="cardOrTableArr"
      @cardFn="cardFn"
      @LoadMore="LoadMore"
      @loadingComplete="loadingComplete"
      :LoadMoreLoadingIsShow="LoadMoreLoadingIsShow"
      :processedComponent="processedComponent"
      @openProcessed="openProcessed"
      :NewactiveClickArr1="NewactiveClickArr"
      :CurrentlyCheckedId="CurrentlyCheckedId"
      :activeClickstyleDocTreeIdArr="activeClickstyleDocTreeIdArr"
    ></component>
    <div class="pagination" v-if="mode != 3">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[30, 60, 90, 120]"
        layout="total, sizes, prev, pager,"
        :total="queryParams.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog v-model="decideDialogVisible" title="裁断" :fullscreen="true" header-class="decideDialogVisibleHeader">
      <div style="display: flex">
        <span style="margin-right: 5px" v-if="!isFlipped">
          <el-button type="primary" @click="toggleWidth">{{
            toggleState === 0 ? '切换制令列表' : toggleState === 1 ? '切换部件列表' : '切换全部'
          }}</el-button></span
        >
        <div style="width: 100%; display: flex; justify-content: right">
          <span style="color: #409eff; margin-right: 5px; margin-bottom: 2px" v-if="!isFlipped"
            >每小包数：<el-input v-model="packetNumber" style="width: 100px"
          /></span>
          <span style="margin-right: 5px" v-if="!isFlipped">
            <el-button type="primary" @click="shengcheng" :loading="shengchengLoading">生成部件码</el-button></span
          >
          <span style="margin-right: 5px" v-if="isFlipped">
            <el-button type="primary" @click="SwitchDecideBottom">
              {{ SwitchDecideBottomId == 1 ? '切换已打印' : SwitchDecideBottomId == 2 ? '切换全部' : '切换未打印' }}</el-button
            ></span
          >
          <span>
            <el-button type="primary" @click="isFlipped = !isFlipped">{{ isFlipped ? '返回主列表' : '查看部件列表' }}</el-button></span
          >
        </div>
      </div>
      <span>
        <div class="flip-container" :class="{ 'flipped': isFlipped }">
          <div class="flipper">
            <!-- 正面内容 -->
            <div class="front">
              <div class="one">
                <div v-if="firstDivVisible" :style="{ width: firstDivWidth }">
                  <div class="decideLeftBox" style="height: 100%">
                    <vxe-grid
                      class="mylist-table-left"
                      ref="decideLeftRef"
                      v-bind="decideLeft"
                      height="100%"
                      empty-text="没有数据!"
                      width="100%"
                      @cell-click="leftCellDblclick"
                      :key="decideLeftKey"
                    >
                      <template #edit_Size="{ row, column }">
                        <el-input v-model="row[column.property]" @change="row.sizeMap[column.property] = Number(row[column.property])"> </el-input>
                      </template>
                    </vxe-grid>
                  </div>
                </div>
                <div v-if="secondDivVisible" :style="{ width: secondDivWidth }">
                  <div class="decideLeftBox" style="height: 100%">
                    <vxe-grid
                      class="mylist-table-right"
                      ref="decideRightRef"
                      v-bind="decideRight"
                      height="100%"
                      empty-text="没有数据!"
                      width="100%"
                      :key="decideRightKey"
                      @checkboxChange="decideRightCheckboxChange"
                      @checkboxAll="decideRightCheckboxAll"
                    >
                      <template #default_user="{ row, rowIndex }">
                        <el-button v-if="!row.taskUserName && row.isShowModify" type="primary" link @click="openUserList(row)">选择</el-button>
                        <span v-else
                          >{{ row.taskUserName }}
                          <el-button v-if="row.ModifyUser" type="primary" link @click="openUserList(row, true)">重选</el-button>
                          <el-button v-if="row.taskUserId" link @click="synchronizationUser(row, rowIndex)"
                            ><el-icon color="#409EFF"><Switch /></el-icon
                          ></el-button>
                        </span>
                      </template>
                      <template #edit_TextInput="{ row, column }">
                        <el-input v-model="row[column.property]" @change="row.isShowModify = true" />
                      </template>
                      <template #edit_taskUserName="{ row, rowIndex }">
                        <el-button v-if="!row.taskUserName && row.isShowModify" type="primary" link @click="openUserList(row)">选择</el-button>
                        <span v-else
                          >{{ row.taskUserName }}
                          <el-button v-if="row.ModifyUser" type="primary" link @click="openUserList(row, true)">重选</el-button>
                          <el-button v-if="row.taskUserId" link @click="synchronizationUser(row, rowIndex)"
                            ><el-icon color="#409EFF"><Switch /></el-icon
                          ></el-button>
                        </span>
                      </template>
                    </vxe-grid>
                  </div>
                </div>
              </div>
            </div>
            <!-- 背面内容 -->
            <div class="back">
              <div class="two" style="height: 100%">
                <vxe-grid
                  class="mylist-table"
                  ref="decideBottomRef"
                  v-bind="decideBottom"
                  height="100%"
                  empty-text="没有数据!"
                  width="100%"
                  :key="decideBottomKey"
                >
                  <template #default_qrCodeImage="{ row, column }">
                    <span
                      >{{ row[column.property]
                      }}<el-button
                        :type="row.type ? 'success' : 'primary'"
                        link
                        @click="
                          openQrCodeImage(row[column.property]);
                          row.type = 'success';
                        "
                        >打开</el-button
                      ></span
                    >
                  </template>
                </vxe-grid>
              </div>
            </div>
          </div>
        </div>
      </span>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="goReprintCode" v-if="isFlipped">补打条码</el-button>
          <!-- <el-button type="primary" @click="goDistributionSheet" v-if="!isFlipped">打印分配单</el-button> -->
          <el-button type="primary" @click="goPrintComponentCode" v-if="isFlipped">打印部件码</el-button>

          <el-button
            @click="
              // getOrderStyleStitchingListFn();
              colosedecideDialogVisible()
            "
            >关闭</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="QrCodeImagedialogVisible" title="二维码" width="500" :close-on-click-modal="true">
      <span><img :src="qrCodeImage" alt="QR Code" /></span>
    </el-dialog>
    <el-dialog v-model="userListDialog" title="裁断用户列表" width="85%">
      <span
        ><el-table :data="userListData" border height="500px" @row-dblclick="userListDataDblclick">
          <el-table-column property="userName" label="用户昵称" />
          <el-table-column property="deptName" label="部门" /> </el-table
      ></span>
    </el-dialog>
    <el-dialog v-model="DistributionSheetIsShow" title="打印分配单" width="80%">
      <i-frame :src="DistributionSheetIsShowOpenUrl" id="jimuReportFrame" v-if="DistributionSheetIsShow"></i-frame>
    </el-dialog>
    <Outsole
      v-if="OutsoleIsShow"
      @closeOutsole="closeOutsole"
      :OutsoleList="OutsoleList"
      :activeClickstyleDocTreeIdArr="activeClickstyleDocTreeIdArr"
      :taskConfigurationId="taskConfigurationId"
    ></Outsole>
    <processed
      v-if="processedIsShow"
      @closeProcessedDialog="closeProcessedDialog"
      :openprocessedNumber="openprocessedNumber"
      :openprocesseItem="openprocesseItem"
    ></processed>
    <el-dialog v-model="ReprintCodeDialog" title="补打条码" width="80%">
      <span>
        <el-input-number v-model="ReprintCodeNumber" :min="1" :max="ReprintCodeMaxNumber" />
      </span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="ReprintCodeDialog = false">关闭</el-button>
          <el-button type="primary" @click="SureReprintCode"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script  setup name="Decide">
import QRCode from 'qrcode';
import { Search, Close, Refresh } from '@element-plus/icons-vue';
import designate from './components/designate.vue';
import delivery from './components/delivery.vue';
import card from './components/card.vue';
import processed from './components/processed.vue';
import overlay from './components/Overlay.vue';
import list from './components/list.vue';
import Outsole from './components/Outsole.vue';
import {
  getOrderStyleStitchingList,
  getOrderStyleStitchingDetail,
  getOrderStyleStitchingMaterialByOrderTreeId,
  getconfigKeySys_config_packet_number,
  getMesTaskAssignCuttingIdByStyleDocTreeId,
  creatGeneratedComponentCode,
  printMesTaskAssignCuttingCode,
  getUserListByTaskName,
  reprintMesTaskAssignCuttingCode
} from '@/api/mes/decide';
import { getOrderStyleBondingDetail } from '@/api/mes/Outsole';
import { reactive } from 'vue';
import { index } from './index';
import { Loading } from '@element-plus/icons-vue';
import { ElLoading } from 'element-plus';
const { proxy } = getCurrentInstance();
const { decideLeft, decideRight, decideBottom } = index();
let activeName = reactive({
  name: card
});
const buttons = [
  { id: 1, text: 'loading', name: markRaw(overlay) },
  { id: 2, text: '卡片模式', name: markRaw(card) },
  { id: 3, text: '已处理列表模式', name: markRaw(processed) },
  { id: 4, text: '列表模式', name: markRaw(list) }
];
const shortcuts = [
  {
    text: '整周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '半月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    }
  },
  {
    text: '一月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '半年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
      return [start, end];
    }
  }
];
const queryFormRef = ref();
const queryParams = ref({ schedule: '', pageNum: 1, pageSize: 30, total: 0 });
// 遮罩层状态
const showMask = ref(false);
const cardOrTableArr = ref([]);
const scheduleOptions = ref([]);
const mode = ref(1);
const componentMap = {
  1: card,
  2: list,
  3: processed
};
const getOrderStyleStitchingListFn = () => {
  // activeName.name = overlay;
  showMask.value = true;
  getOrderStyleStitchingList(queryParams.value)
    .then((res) => {
      scheduleOptions.value = res.data.records;
      cardOrTableArr.value = res.data.records;
      // cardOrTableArr.value.map((item) => {
      //   item.mesTaskAssignList.map((item1, index) => {
      //     item1.isComplete = index;
      //   });
      // });
      queryParams.value.total = res.data.total;
      showMask.value = false;
      proxy?.$modal.msgSuccess('查询成功');
      if (mode == 1) {
        activeName.name = card;
      } else if (mode == 2) {
        activeName.name = list;
      } else if (mode == 3) {
        activeName.name = processed;
      }
    })
    .catch((err) => {
      showMask.value = false;
      activeName.name = mode == 1 ? (activeName.name = card) : (activeName.name = card);
    });
};
let LoadMoreLoadingIsShow = ref(true);
const LoadMore = async () => {
  // queryParams.value.pageSize += 30;
  queryParams.value.pageNum += 1;

  await getOrderStyleStitchingList(queryParams.value).then((res) => {
    cardOrTableArr.value = [...cardOrTableArr.value, ...res.data.records];
    if (queryParams.value.pageNum * queryParams.value.pageSize >= res.data.total) {
      LoadMoreLoadingIsShow.value = false;
    }
    loadingComplete(LoadMoreLoadingIsShow.value);
  });
};
const dynamicComponent = ref();
const loadingComplete = (val) => {
  dynamicComponent.value.loadingComplete(val);
};

onMounted(() => {
  getOrderStyleStitchingListFn();
});
const resetQuery = () => {
  // 重置查询参数
  queryFormRef.value.resetFields();
  queryParams.value.pageNum = 1;
  cardOrTableArr.value = [];
  CurrentlyCheckedId.value = [];
  activeClickstyleDocTreeIdArr.value = [];
  dynamicComponent.value.clearBorder();
  // 重新调用查询方法，模拟首次进入页面的加载
  getOrderStyleStitchingListFn();
  // console.log(queryParams.value);
};
let decideDialogVisible = ref(false);
let decideLeftKey = ref(Math.random());
let taskConfigurationId = ref({});
const OutsoleIsShow = ref(false);
const opendesignate = (val) => {
  // console.log(val)
  taskConfigurationId.value.mesTaskConfigurationId = val;
  if (val == '1905509608912551938') {
    getOrderStyleStitchingDetail(CurrentlyCheckedId.value).then((res) => {
      decideLeft.data = res.data;
      leftCellDblclick({ row: res.data[0] });
      let sizeMap = res.data[0].mergedSizeList;
      decideLeft.data.forEach((item) => {
        sizeMap.map((item1) => {
          item[item1] = item.sizeMap[item1];
        });
      });
      decideLeft.columns[3].children = [];

      for (let i = 0; i < sizeMap.length; i++) {
        const obj = {
          field: sizeMap[i],
          title: sizeMap[i],
          width: 88,
          align: 'center',
          editRender: {
            changeColor: 'blue'
          },
          slots: { edit: 'edit_Size' }
        };
        decideLeft.columns[3].children.push(obj);
      }
      decideLeft.columns[3].children.sort((a, b) => {
        const sizeA = parseFloat(a.title);
        const sizeB = parseFloat(b.title);
        return sizeA - sizeB;
      });
      decideLeft.loading = false;
      decideRight.data = [];
      decideBottom.data = [];
      activSelectRightRow.value = [];
      decideLeftKey.value = Math.random();
      isFlipped.value = false;

      decideDialogVisible.value = true;
    });
    getconfigKeySys_config_packet_number().then((res) => {
      packetNumber.value = res.data;
    });
  } else if (val.mesTaskConfigurationId == 'f43c91b2-6be8-4b43-bd09-1db30adab6b8') {
    OutsoleIsShow.value = true;
    getOrderStyleBondingDetail(CurrentlyCheckedId.value).then((res) => {
      OutsoleList.value = res.data;
    });
  }
};

//当前勾选的数据
const CurrentlyCheckedId = ref([]);
const activeClickstyleDocTreeIdArr = ref([]);
const NewactiveClickArr = ref([]);
const cardFn = (ids, styleDocTreeId, NewactiveClickArr1) => {
  CurrentlyCheckedId.value = ids;
  activeClickstyleDocTreeIdArr.value = styleDocTreeId;
  NewactiveClickArr.value = NewactiveClickArr1;
  console.log(ids, styleDocTreeId, NewactiveClickArr1)
};
const inputFilterRender = reactive({
  name: 'VxeInput'
});
const decideLeftRef = ref();
let activeleftCellDblclickStyleDocTreeId;
let activeleftCellDblclickRow;
const leftCellDblclick = async ({ row }) => {
  decideRight.loading = true;
  activeleftCellDblclickStyleDocTreeId = row.styleDocTreeId;
  activeleftCellDblclickRow = row;
  await getOrderStyleStitchingMaterialByOrderTreeIdFn(row.styleDocTreeId);
  await getMesTaskAssignCuttingIdByStyleDocTreeIdFn(row.styleDocTreeId);
};
const decideRightRef = ref();
let decideRightKey = ref(Math.random());
const getOrderStyleStitchingMaterialByOrderTreeIdFn = async (styleDocTreeId) => {
  await getOrderStyleStitchingMaterialByOrderTreeId(styleDocTreeId).then((res) => {
    decideRight.data = res.data;
    decideRight.data.forEach((obj) => {
      // 如果 obj.name 不存在或者为空，则将 obj.name 设置为 obj.test 的值
      if (!obj.units) {
        obj.units = obj.partName;
      }
    });
    decideRight.loading = false;
    const filtersFields = ['partName', 'units', 'materialName', 'taskUserName'];
    proxy.filtersFieldsFn(filtersFields, decideRight);
  });
  setTimeout(() => {
    decideRightKey.value = Math.random();
  }, 100);
};
const decideBottomKey = ref(Math.random());
const getMesTaskAssignCuttingIdByStyleDocTreeIdFn = async (styleDocTreeId) => {
  decideBottom.loading = true;
  await getMesTaskAssignCuttingIdByStyleDocTreeId(styleDocTreeId).then((res) => {
    decideBottom.data = SwitchDecideBottomFn(res.data);
    decideBottom.loading = false;
  });
  const filtersFields = [
    'partName',
    'units',
    'orderStyleSizeName',
    'assignQuantity',
    'partQrCode',
    'orderStyleColorName',
    'materialName',
    'taskUserName',
    'sessionType',
    'styleCode',
    'customStyleCode',
    'packetCount',
    'printCount'
  ];
  proxy.filtersFieldsFn(filtersFields, decideBottom);
  setTimeout(() => {
    decideBottomKey.value = Math.random();
  }, 100);
};
const SwitchDecideBottomFn = (data) => {
  console.log(SwitchDecideBottomId.value);
  let filterData = [];
  if (SwitchDecideBottomId.value == 1) {
    filterData = data.filter((item) => item.printCount == 0);
  } else if (SwitchDecideBottomId.value == 2) {
    filterData = data.filter((item) => item.printCount >= 1);
  } else {
    filterData = data;
  }

  return filterData;
};
const packetNumber = ref();
const qrCodeImage = ref();
const QrCodeImagedialogVisible = ref(false);
const openQrCodeImage = (val) => {
  QRCode.toDataURL(val, (err, url) => {
    QrCodeImagedialogVisible.value = true;
    qrCodeImage.value = url;
  });
};
let activeDecideRightRow;
let userListDialog = ref(false);
const userListData = ref([]);
const ReselectIsShow = ref(false);
const openUserList = (row, Reselect) => {
  // console.log(row)
  activeDecideRightRow = row;
  ReselectIsShow.value = Reselect;
  getUserListByTaskName(taskConfigurationId.value.mesTaskConfigurationId).then((res) => {
    userListData.value = res.data;
    userListDialog.value = true;
  });
};

const userListDataDblclick = (row) => {
  const $grid = decideRightRef.value;
  const selectRecords = $grid.getCheckboxRecords();
  selectRecords.map((item) => {
    item.taskUserName = row.userName;
    item.taskUserId = row.userId;
    item.isShowModify = true;
    item.ModifyUser = false;
  });
  $grid.clearCheckboxRow();
  userListDialog.value = false;
  // activeDecideRightRow.taskUserName = row.userName;
  // activeDecideRightRow.taskUserId = row.userId;
  // userListDialog.value = false;
  // const $grid = decideRightRef.value;
  // $grid.setCheckboxRow(activeDecideRightRow, true);
  // if (ReselectIsShow.value && activeDecideRightRow.isGeneratedComponentCode == 1) {
  //   decideRight.data
  //     .filter((item) => item.isGeneratedComponentCode == 1)
  //     .map((item) => {
  //       item.taskUserName = row.userName;
  //       item.taskUserId = row.userId;
  //     });
  // }
};
const synchronizationUser = (row, index) => {
  const $grid = decideRightRef.value;
  $grid.setCheckboxRow(decideRight.data[index], true);
  decideRight.data.slice(index).map((item) => {
    item.taskUserName = row.taskUserName;
    item.taskUserId = row.taskUserId;
    $grid.setCheckboxRow(item, true);
  });
};

let activSelectRightRow = ref([]);
const decideRightCheckboxAll = (val) => {
  console.log(val);
  activSelectRightRow.value = val.records;
  if (val.records.length == 0) {
    decideRight.data.map((item) => {
      item.isShowModify = false;
      item.ModifyUser = false;
    });
  } else {
    val.records.map((item) => {
      item.isShowModify = true;
      item.ModifyUser = true;
    });
  }
};
const decideRightCheckboxChange = (val) => {
  activSelectRightRow.value = val.records;
  val.row.isShowModify = !val.row.isShowModify;
  val.row.ModifyUser = !val.row.ModifyUser;
};
const isFlipped = ref(false);
const clickedGenerate = ref(false); //是否点击过生成
const shengchengLoading = ref(false);
const shengcheng = async () => {
  const $grid = decideRightRef.value;
  // const filterMesOrderStyleStitchingMaterialResponseDtoList = decideRight.data.filter((item) => item.isShowModify);
  // const taskUserName = [];
  // for (let i = 0; i < filterMesOrderStyleStitchingMaterialResponseDtoList.length; i++) {
  //   if (!filterMesOrderStyleStitchingMaterialResponseDtoList[i].taskUserName) {
  //     taskUserName.push({ index: i + 1 });
  //   }
  // }
  // if (taskUserName.length > 0) {
  //   proxy?.$modal.msgWarning(`第${taskUserName.map((item) => item.index).join(',')}行负责人不能为空`);
  //   return;
  // }
  if ($grid) {
    const newObj = {
      mesOrderStyleStitchingListId: activeleftCellDblclickRow.mesOrderStyleStitchingListId,
      styleDocTreeId: activeleftCellDblclickRow.styleDocTreeId,
      customStyleCode: activeleftCellDblclickRow.customStyleCode,
      totalCount: activeleftCellDblclickRow.totalCount,
      styleCode: activeleftCellDblclickRow.styleCode,
      shipmentDate: activeleftCellDblclickRow.shipmentDate,
      orderStyleColorName: activeleftCellDblclickRow.orderStyleColorName,
      sizeMap: activeleftCellDblclickRow.sizeMap,
      sizeCode: activeleftCellDblclickRow.sizeCode,
      sessionType: activeleftCellDblclickRow.sessionType,
      packetNumber: packetNumber.value,
      mesOrderStyleStitchingMaterialResponseDtoList: decideRight.data.filter((item) => item.isShowModify),
      mesTaskConfigurationId: taskConfigurationId.value.mesTaskConfigurationId,
      mergedSizeList: activeleftCellDblclickRow.mergedSizeList
    };
    // shengchengLoading.value=true;
    const loading = ElLoading.service({
      lock: true,
      text: '生成中，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    await creatGeneratedComponentCode([newObj])
      .then(async (res) => {
        if (res.msg == '该订单条码已打印,是否确定重新生成?') {
          ElMessageBox.confirm(res.msg, '提醒', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            twoshengcheng();
          });
        } else {
          proxy?.$modal.msgSuccess('生成成功');
          isFlipped.value = true;
          clickedGenerate.value = true;
          await getMesTaskAssignCuttingIdByStyleDocTreeIdFn(activeleftCellDblclickStyleDocTreeId);
          await getOrderStyleStitchingMaterialByOrderTreeIdFn(activeleftCellDblclickStyleDocTreeId);
        }
      })
      .catch((err) => {
        // console.log(err);
        // ElMessageBox.alert(err, {
        //   confirmButtonText: '确定',
        //   callback: (action) => {
        //     ElMessage({
        //       type: 'info',
        //       message: `action: ${action}`
        //     });
        //   }
        // });
        // proxy?.$modal.msgError('生成失败');
      })
      .finally(() => {
        loading.close();
      });
  }
};
const twoshengcheng = async () => {
  const newObj = {
    mesOrderStyleStitchingListId: activeleftCellDblclickRow.mesOrderStyleStitchingListId,
    styleDocTreeId: activeleftCellDblclickRow.styleDocTreeId,
    customStyleCode: activeleftCellDblclickRow.customStyleCode,
    totalCount: activeleftCellDblclickRow.totalCount,
    styleCode: activeleftCellDblclickRow.styleCode,
    shipmentDate: activeleftCellDblclickRow.shipmentDate,
    orderStyleColorName: activeleftCellDblclickRow.orderStyleColorName,
    sizeMap: activeleftCellDblclickRow.sizeMap,
    sizeCode: activeleftCellDblclickRow.sizeCode,
    sessionType: activeleftCellDblclickRow.sessionType,
    packetNumber: packetNumber.value,
    mesOrderStyleStitchingMaterialResponseDtoList: decideRight.data.filter((item) => item.isShowModify),
    mesTaskConfigurationId: taskConfigurationId.value.mesTaskConfigurationId,
    mergedSizeList: activeleftCellDblclickRow.mergedSizeList,
    isConfirm: 1
  };
  const loading = ElLoading.service({
    lock: true,
    text: '生成中，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  await creatGeneratedComponentCode([newObj])
    .then(async (res) => {
      proxy?.$modal.msgSuccess('生成成功');
      isFlipped.value = true;
      clickedGenerate.value = true;
      await getMesTaskAssignCuttingIdByStyleDocTreeIdFn(activeleftCellDblclickStyleDocTreeId);
      await getOrderStyleStitchingMaterialByOrderTreeIdFn(activeleftCellDblclickStyleDocTreeId);
    })
    .finally(() => {
      loading.close();
    });
};
const decideBottomRef = ref();
const DistributionSheetIsShowOpenUrl = ref();
const DistributionSheetIsShow = ref(false);
const goDistributionSheet = () => {
  DistributionSheetIsShowOpenUrl.value = `${
    import.meta.env.VITE_APP_BASE_URL3
  }/jmreport/view/1072032285982216192?style_doc_tree_id=${activeleftCellDblclickStyleDocTreeId}`;
  DistributionSheetIsShow.value = true;
  // console.log()
};
const goDistributionSheet2 = () => {
  // console.log(openprocesseItem.value)
  // return
  if (CurrentlyCheckedId.value.length > 1) {
    proxy?.$modal.msgWarning('只能选择一条数据');
    return;
  }
  DistributionSheetIsShowOpenUrl.value = `${import.meta.env.VITE_APP_BASE_URL3}/jmreport/view/1072032285982216192?style_doc_tree_id=${
    NewactiveClickArr.value[0].styleDocTreeId
  }`;
  DistributionSheetIsShow.value = true;
};
const colosedecideDialogVisible = () => {
  decideDialogVisible.value = false;
  if (clickedGenerate.value) {
    decideLeft.data.map((item) => {
      let index = cardOrTableArr.value.findIndex((obj) => obj.styleDocTreeId === item.styleDocTreeId);
      cardOrTableArr.value.splice(index, 1);
    });
    decideLeft.data = [];
    cardFn([]);
    dynamicComponent.value.clearBorder();
  }
};
const handleSizeChange = (val) => {
  getOrderStyleStitchingListFn();
  console.log(`${val} items per page`);
};
const handleCurrentChange = (val) => {
  getOrderStyleStitchingListFn();
  console.log(`current page: ${val}`);
};
const processedComponent = ref(1);
const processedExamine = (val) => {
  mode.value = 3;
  activeName.name = processed;
  processedComponent.value = val;
};
const goPrintComponentCode = async () => {
  const $grid = decideBottomRef.value;
  const selectRecords = $grid.getCheckboxRecords();
  if (selectRecords.length == 0) {
    proxy?.$modal.msgWarning('请选择一条数据');
    return;
  }
  await printMesTaskAssignCuttingCode(selectRecords).then((res) => {});
  await getMesTaskAssignCuttingIdByStyleDocTreeIdFn(activeleftCellDblclickStyleDocTreeId);
};
const closeOutsole = () => {
  OutsoleIsShow.value = false;
};
const OutsoleList = ref();
const processedExamineid = ref(1);
const processedIsShow = ref(false);
let openprocessedNumber = ref();
let openprocesseItem = ref();
const openProcessed = (val, item) => {
  processedIsShow.value = true;
  openprocessedNumber.value = val;
  openprocesseItem.value = item;
  console.log(val,item)
};

// 添加关闭处理函数
const closeProcessedDialog = (val) => {
  processedIsShow.value = val;
};
const ReprintCodeDialog = ref(false);
const ReprintCodeNumber = ref(1);
let ReprintCodeMaxNumber = ref(0);
const goReprintCode = () => {
  const $grid = decideBottomRef.value;
  const selectRecords = $grid.getCheckboxRecords();
  if (selectRecords.length == 0) {
    proxy?.$modal.msgWarning('请选择一条数据');
    return;
  }
  ReprintCodeDialog.value = true;
  const minPacketCount = selectRecords.reduce((min, record) => (record.packetCount < min ? record.packetCount : min), selectRecords[0].packetCount);
  ReprintCodeMaxNumber.value = minPacketCount;
};
const SureReprintCode = () => {
  const $grid = decideBottomRef.value;
  const selectRecords = $grid.getCheckboxRecords();

  const deppselectRecords = JSON.parse(JSON.stringify(selectRecords));
  deppselectRecords.map((item) => {
    item.assignQuantity = item.assignQuantity / item.packetCount;
    item.packetCount = 1;
  });
  reprintMesTaskAssignCuttingCode({ printCount: ReprintCodeNumber.value, cuttingCodeList: deppselectRecords }).then((res) => {
    ReprintCodeDialog.value = false;
  });
  console.log(selectRecords);
};
const toggleState = ref(0); // 0: default, 1: first div 100%, 2: second div 100%
const firstDivWidth = ref('70%');
const secondDivWidth = ref('29%');
const firstDivVisible = ref(true);
const secondDivVisible = ref(true);

// Replace the existing toggleWidth function with this one
const toggleWidth = () => {
  if (toggleState.value === 0) {
    // First state: first div 100%, second div hidden
    firstDivWidth.value = '100%';
    secondDivVisible.value = false;
    toggleState.value = 1;
  } else if (toggleState.value === 1) {
    // Second state: first div hidden, second div 100%
    firstDivVisible.value = false;
    secondDivVisible.value = true;
    secondDivWidth.value = '100%';
    toggleState.value = 2;
  } else {
    // Third state: back to default (70% and 29%)
    firstDivVisible.value = true;
    secondDivVisible.value = true;
    firstDivWidth.value = '70%';
    secondDivWidth.value = '29%';
    toggleState.value = 0;
  }
};
const SwitchDecideBottomId = ref(1);
const SwitchDecideBottom = () => {
  if (SwitchDecideBottomId.value > 2) {
    SwitchDecideBottomId.value = 0;
  }
  SwitchDecideBottomId.value++;
  decideBottom.loading = true;
  getMesTaskAssignCuttingIdByStyleDocTreeId(activeleftCellDblclickStyleDocTreeId).then((res) => {
    decideBottom.data = SwitchDecideBottomFn(res.data);
    const filtersFields = [
      'partName',
      'units',
      'orderStyleSizeName',
      'assignQuantity',
      'partQrCode',
      'orderStyleColorName',
      'materialName',
      'taskUserName',
      'sessionType',
      'styleCode',
      'customStyleCode',
      'packetCount',
      'printCount'
    ];
    proxy.filtersFieldsFn(filtersFields, decideBottom);
    setTimeout(() => {
      decideBottomKey.value = Math.random();
    }, 100);
    decideBottom.loading = false;
  });
};
</script>
<style lang='scss' scoped>
.decide_box {
  min-height: calc(100vh - 84px);
  padding: 10px 0px 0px 20px;
  .card_box {
    margin-top: 10px;
    min-height: calc(100vh - 184px);
    .default_card {
      padding: 10px;
      height: 300px;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); /* 主要的阴影效果 */
      transition: box-shadow 0.3s ease; /* 阴影过渡效果 */
      .top_box {
        // height: 150px;
        display: flex;
        justify-content: space-between;
        .top_box_left {
          width: 49%;
          height: 180px;
          background-color: aquamarine;
        }
        .top_box_right {
          width: 49%;
          height: 150px;
          p {
            margin: 8px 0;
          }
        }
      }
    }
    .Select_card {
      border: 2px solid #409eff;
    }
  }
}
:deep(.el-form-item) {
  margin-bottom: 2px !important;
}
.decideLeftBox {
  .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
  .vxe-table--render-default .vxe-header--column:not(.col--ellipsis),
  .vxe-table--render-default.is--padding .vxe-body--column:not(.col--ellipsis) {
    padding: 0px !important;
  }
  :deep(.vxe-table--render-default) {
    padding: 0px !important;
  }
  :deep(.vxe-footer--column:not(.col--ellipsis)) {
    padding: 0px !important;
  }
  :deep(.vxe-header--column:not(.col--ellipsis)) {
    padding: 1px !important;
  }
}

:deep(.el-dialog__header) {
  padding: 10px !important;
}
:deep(.el-dialog__body) {
  padding: 0px 0px !important;
}
:deep(.el-dialog__body) {
  max-height: calc(100vh - 100px) !important;
}
:deep(.el-overlay .el-overlay-dialog .el-dialog .el-dialog__body) {
  padding: 2px !important;
}
.elmBlue {
  color: #409eff;
}
/* 翻页动画样式 */
.flip-container {
  perspective: 1000px;
  position: relative;
  // height: 540px !important;
  height: calc(100vh - 180px) !important;
}

.flipper {
  transition: 0.3s;
  transform-style: preserve-3d;
  position: relative;
  height: 100%;
}

.front,
.back {
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.front {
  z-index: 2;
  transform: rotateY(0deg);
}

.back {
  transform: rotateY(180deg);
}

.flip-container.flipped .flipper {
  transform: rotateY(180deg);
}

/* 原有布局样式保持 */
.one {
  display: flex;
  justify-content: space-between;
  // height: 540px;
  height: 100%;
}

.mylist-table {
  height: 100% !important;
}
::v-deep(.mylist-table-left.vxe-grid .vxe-header--column.col-blue) {
  color: #409eff;
}
::v-deep(.mylist-table-right.vxe-grid .vxe-header--column.col-blue) {
  color: #409eff;
}
::v-deep(.decideLeft-modal) {
  div {
    top: 0px !important;
  }
}
::v-deep(.decideRight-modal) {
  div {
    top: 0px !important;
  }
}
.pagination {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: right;
  padding-right: 20px;
}
</style>
