// 自定义国际化配置
import { createI18n } from 'vue-i18n';

import { LanguageEnum } from '@/enums/LanguageEnum';
import zh_CN from '@/lang/zh_CN';
import en_US from '@/lang/en_US';

/**
 * 获取当前语言
 * @returns zh-cn|en ...
 */
import axios from 'axios';
export const getLanguage = (): LanguageEnum => {
  const language = useStorage<LanguageEnum>('language', LanguageEnum.zh_CN);
  if (language.value) {
    // console.log(language.value)
    return language.value;
  }
  return LanguageEnum.zh_CN;
};
const getAxiosLang=async(lang:any)=>{
  let test 
  await axios.get(`/api/system/user/lang/${lang}`).then(res=>{
    test= res.data.data
  });
  return test
}

const i18n = createI18n ({
  globalInjection: true,
  allowComposition: true,
  legacy: false,
  locale: getLanguage(),
  messages: {
    zh_CN: zh_CN,
    en_US: en_US,
    // zh_CN: await getAxiosLang(getLanguage()),
    // en_US: await getAxiosLang(getLanguage())
  }
});

export default i18n;

export type LanguageType = typeof zh_CN;
