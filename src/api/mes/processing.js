import request from '@/utils/request';

// 查询针车制令列表
export function getTaskAssignProcessListByKeyWord(keyword) {
  return request({
    url: `/system/taskAssignProcess/getTaskAssignProcessListByKeyWord?keyword=${keyword}`,
    method: 'get'
  });
}

//根据派单号获取针车派工详细信息
export function getProcessVerticalSizeDetailListByDispatchOrderNumber(id) {
  return request({
    url: `/system/taskAssignProcess/getProcessVerticalSizeDetailListByDispatchOrderNumber?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

//查询缺陷列表
export function getProcessDefectDetailList(id) {
  return request({
    url: `/system/processDefectDetail/getProcessDefectDetailList?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

//查询针车品检列表top3
export function getTopProcessDefectDetai(id) {
  return request({
    url: `/system/processDefectDetail/getTopProcessDefectDetail?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

export function getSizeDetailListByDispatchOrderNumber(id) {
  return request({
    // url: `/system/taskAssignSewing/getSizeDetailListByMesTaskAssignSewingId?mesTaskAssignSewingId=${id}`,
    url: `/system/taskAssignProcess/getSizeDetailListByDispatchOrderNumber?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

export function addProcessInspection(data) {
  return request({
    url: `/system/processInspection/addProcessInspection`,
    method: 'post',
    data
  });
}

//新增针车品检(手工单)
export function addProcessInspectionBatch(data) {
  return request({
    url: `/system/processInspection/addProcessInspectionBatch`,
    method: 'post',
    data
  });
}
