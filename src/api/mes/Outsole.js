import request from '@/utils/request';

//获取贴底派工明细
export function getOrderStyleBondingDetail(data) {
  return request({
    url: `/system/orderStyleStitchingList/getOrderStyleBondingDetail`,
    method: 'post',
    data
  });
}

//贴底每箱双数
export function sys_cofig_box_number() {
  return request({
    url: `/system/config/configKey/sys_cofig_box_number`,
    method: 'get',
  });
}

//生成箱码
export function creatGeneratedBondingCode(data) {
  return request({
    url: `/system/taskAssignBondingCode/creatGeneratedBondingCode`,
    method: 'post',
    data
  });
}

//获取箱码详情
export function getGeneratedBondingCodeDetail(id) {
  return request({
    url: `/system/taskAssignBondingCode/getGeneratedBondingCodeDetail?styleDocTreeId=${id}`,
    method: 'post',
  });
}

//查询用户列表根据任务名称
export function getUserListByTaskName(id) {
  return request({
    url: `/system/taskConfiguration/getUserListByTaskMesTaskConfigurationId?taskConfigurationId=${id}`,
    method: 'get',
  });
}

//打印
export function printBondingCode(data) {
  return request({
    url: `/system/taskAssignBondingCode/printBondingCode`,
    method: 'post',
    data
  });
}
export function reprintBondingCode(data) {
  return request({
    url: `/system/taskAssignBondingCode/reprintBondingCode`,
    method: 'post',
    data
  });
}
