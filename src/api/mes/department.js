import request from '@/utils/request';

//根据关键词获取针车派工详细信息
export function getTaskAssignSewingListByKeyWord(keyword) {
  return request({
    url: `/system/taskAssignSewing/getTaskAssignSewingListByKeyWord?keyword=${keyword}`,
    method: 'get'
  });
}

export function getSizeDetailListByDispatchOrderNumber(id) {
  return request({
    // url: `/system/taskAssignSewing/getSizeDetailListByMesTaskAssignSewingId?mesTaskAssignSewingId=${id}`,
    url: `/system/taskAssignSewing/getSizeDetailListByDispatchOrderNumber?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

//查询十大不良数据列表(为空-全部 1-十大 2-其它)
export function getSysSewingUndesirableReason(id, type) {
  return request({
    url: `/system/dict/data/getSysSewingUndesirableReason?status=${id}&dictType=${type}`,
    method: 'get'
  });
}

//查询缺陷列表
export function getSewingDefectDetailList(id) {
  return request({
    url: `/system/sewingDefectDetail/getSewingDefectDetailList?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

//查询针车品检列表top3
export function getTopSewingDefectDetail(id) {
  return request({
    url: `/system/sewingDefectDetail/getTopSewingDefectDetail?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

//新增针车品检
export function addSewingInspection(data) {
  return request({
    url: `/system/sewingInspection/addSewingInspection`,
    method: 'post',
    data
  });
}

//根据派单号获取针车派工详细信息
export function getVerticalSizeDetailListByDispatchOrderNumber(id) {
  return request({
    url: `/system/taskAssignSewing/getVerticalSizeDetailListByDispatchOrderNumber?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

//查询针车产出列表
export function sewingOutputlist(id) {
  return request({
    url: `/system/sewingOutput/list?dispatchOrderNumber=${id}`,
    method: 'get'
  });
}

export function sewingOutputprint(data) {
  return request({
    url: `/system/sewingOutput/print`,
    method: 'post',
    data
  });
}

export function sewingOutputreprint(data) {
  return request({
    url: `/system/sewingOutput/reprint`,
    method: 'post',
    data
  });
}

//新增针车品检(手工单)
export function addSewingInspectionBatch(data) {
  return request({
    url: `/system/sewingInspection/addSewingInspectionBatch`,
    method: 'post',
    data
  });
}
