import request from '@/utils/request';

// 查询针车制令列表
export function getOrderStyleStitchingList(data) {
  return request({
    url: '/system/orderStyleStitchingList/getOrderStyleStitchingList',
    method: 'post',
    data
  });
}

//
export function getOrderStyleStitchingDetail(data) {
  return request({
    url: '/system/orderStyleStitchingList/getOrderStyleStitchingDetail',
    method: 'post',
    data
  });
}

//获取派工物料明细
export function getOrderStyleStitchingMaterialByOrderTreeId(id) {
  return request({
    url: `/system/orderStyleStitchingMaterial/getOrderStyleStitchingMaterialByOrderTreeId?styleDocTreeId=${id}`,
    method: 'get'
  });
}

//查询小包数
export function getconfigKeySys_config_packet_number(id) {
  return request({
    url: `/system/config/configKey/sys_config_packet_number`,
    method: 'get'
  });
}

//裁断派工明细弹窗2
export function getMesTaskAssignCuttingIdByStyleDocTreeId(id) {
  return request({
    // url: `/system/taskAssignCutting/getMesTaskAssignCuttingCodeByStyleDocTreeId?styleDocTreeId=${id}`,
    url: `/system/taskAssignCuttingCode/getMesTaskAssignCuttingCodeByStyleDocTreeId?styleDocTreeId=${id}`,
    method: 'get'
  });
}

//
export function getuserlist() {
  return request({
    url: `system/user/list?pageNum=1&pageSize=100`,
    method: 'get'
  });
}
//
export function getuserlist2(roleId) {
  return request({
    url: `/system/role/user/${roleId}`,
    method: 'get'
  });
}

//
export function addMesOrderStyleStitchingMateriaAssigningTask(data) {
  return request({
    url: `/system/taskAssign/addMesOrderStyleStitchingMateriaAssigningTask`,
    method: 'post',
    data
  });
}

//生成部件码信息
export function creatGeneratedComponentCode(data) {
  return request({
    url: `/system/taskAssignCuttingCode/creatGeneratedComponentCode`,
    method: 'post',
    data
  });
}

//查询任务配置列表
export function gettaskConfigurationlist(data) {
  return request({
    url: `/system/taskConfiguration/list`,
    method: 'get'
  });
}

//任务配置
export function getTaskConfigurationListByRoleIds(data) {
  return request({
    url: '/system/taskConfiguration/getTaskConfigurationListByRoleIds',
    method: 'post',
    data
  });
}

//根据参数获取裁断入库明细详细信息
export function getMesStockCuttingInDetailByParam(data) {
  return request({
    url: '/system/stockCuttingInDetail/getMesStockCuttingInDetailByParam',
    method: 'post',
    data
  });
}

//获取针车派工详细信息
export function getTaskAssignSewingListByParam(data) {
  return request({
    url: '/system/taskAssignSewing/getTaskAssignSewingListByParam',
    method: 'post',
    data
  });
}

//打印部件🐎
export function printMesTaskAssignCuttingCode(data) {
  return request({
    url: '/system/taskAssignCuttingCode/printMesTaskAssignCuttingCode',
    method: 'post',
    data
  });
}
//查询用户列表根据任务名称
export function getUserListByTaskName(id) {
  return request({
    url: `/system/taskConfiguration/getUserListByTaskMesTaskConfigurationId?taskConfigurationId=${id}`,
    method: 'get'
  });
}

//查询贴底入库列表
export function getStockBondingInListByParam(data) {
  return request({
    url: `/system/stockBondingIn/getStockBondingInListByParam`,
    method: 'post',
    data
  });
}

export function getStockProcessInListByParam(data) {
  return request({
    url: `/system/taskAssignProcess/getStockProcessInListByParam`,
    method: 'post',
    data
  });
}

//补打部件码
export function reprintMesTaskAssignCuttingCode(data) {
  return request({
    url: `/system/taskAssignCuttingCode/reprintMesTaskAssignCuttingCode`,
    method: 'post',
    data
  });
}

//裁断配套已完成列表
export function getMesTaskAssignCuttingCodeListByParam(data) {
  return request({
    url: `/system/taskAssignCuttingCode/getMesTaskAssignCuttingCodeListByParam`,
    method: 'post',
    data
  });
}

