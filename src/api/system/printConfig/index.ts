import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PrintConfigVO, PrintConfigForm, PrintConfigQuery } from '@/api/system/printConfig/types';

/**
 * 查询打印机配置列表
 * @param query
 * @returns {*}
 */

export const listPrintConfig = (query?: PrintConfigQuery): AxiosPromise<PrintConfigVO[]> => {
  return request({
    url: '/system/printConfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询打印机配置详细
 * @param mesPrintConfigId
 */
export const getPrintConfig = (mesPrintConfigId: string | number): AxiosPromise<PrintConfigVO> => {
  return request({
    url: '/system/printConfig/' + mesPrintConfigId,
    method: 'get'
  });
};

/**
 * 新增打印机配置
 * @param data
 */
export const addPrintConfig = (data: PrintConfigForm) => {
  return request({
    url: '/system/printConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改打印机配置
 * @param data
 */
export const updatePrintConfig = (data: PrintConfigForm) => {
  return request({
    url: '/system/printConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除打印机配置
 * @param mesPrintConfigId
 */
export const delPrintConfig = (mesPrintConfigId: string | number | Array<string | number>) => {
  return request({
    url: '/system/printConfig/' + mesPrintConfigId,
    method: 'delete'
  });
};

export const getUserIp = () => {
  return request({
    url: `/system/printConfig/getUserIp`,
    method: 'get'
  });
};
