export interface PrintConfigVO {
  /**
   * 打印配置id
   */
  mesPrintConfigId: string | number;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 设备ip
   */
  deviceIp: string;

  /**
   * 打印机ip
   */
  printerIp: string;

  /**
   * 打印机端口
   */
  port: number;

  /**
   * 部门id
   */
  deptId: string | number;

}

export interface PrintConfigForm extends BaseEntity {
  /**
   * 打印配置id
   */
  mesPrintConfigId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 设备ip
   */
  deviceIp?: string;

  /**
   * 打印机ip
   */
  printerIp?: string;

  /**
   * 打印机端口
   */
  port?: number;

  /**
   * 部门id
   */
  deptId?: string | number;

}

export interface PrintConfigQuery extends PageQuery {

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 设备ip
   */
  deviceIp?: string;

  /**
   * 打印机ip
   */
  printerIp?: string;

  /**
   * 打印机端口
   */
  port?: number;

  /**
   * 部门id
   */
  deptId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



