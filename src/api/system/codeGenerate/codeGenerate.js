import request from "@/utils/request";
//保存
export function codeGenerateRuleupdate(data) {
  return request({
    url: "/system/codeGenerateRule/update",
    method: "post",
    data,
  });
}
//查询数据表
export function getTableList(params) {
  return request({
    url: "/system/codeGenerateRule/getTableList",
    method: "get",
    params: params,
  });
}
//获取字段列表
export function getTableColumnList(id) {
  return request({
    url: `/system/codeGenerateRule/getTableColumnList/${id}`,
    method: "get",
  });
}
//查详情
export function TableListdetail(id) {
  return request({
    url: `/system/codeGenerateRule/${id}`,
    method: "get",
  });
}
export function getcodeGenerateRule(params) {
  return request({
    url: `/system/codeGenerateRule/list`,
    method: "get",
    params: params,
  });
}
export function codeGenerateRuleAdd(data) {
  return request({
    url: "/system/codeGenerateRule/add",
    method: "post",
    data,
  });
}
//删除
export function delcodeGenerateRule(id) {
  return request({
    url: `/system/codeGenerateRule/${id}`,
    method: "DELETE",
  });
}
//修改
export function putcodeGenerateRule(data) {
  return request({
    url: "/system/codeGenerateRule/update",
    method: "post",
    data,
  });
}