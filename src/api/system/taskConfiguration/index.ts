import request from '@/utils/request';

// 查询角色列表
export function getRoleList() {
  return request({
    url: '/system/role/list',
    method: 'get',
  
  });
}
// 查询角色列表
export function getRoleList2() {
  return request({
    url: '/system/taskConfiguration/getTaskConfigurationListByRoleIds',
    method: 'post',
  
  });
}
//新增任务列表
export function addOrUpdateMesTaskConfiguration(data) {
    return request({
      url: `/system/taskConfiguration/addOrUpdateMesTaskConfiguration`,
      method: 'post',
        data
    });
  }

//查询任务配置列表
export function gettaskConfigurationlist(data) {
    return request({
      url: `/system/taskConfiguration/list`,
      method: 'get',
    });
  }
  
