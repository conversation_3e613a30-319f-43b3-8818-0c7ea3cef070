<template>
  <teleport to="body">
    <transition name="modal-backdrop" appear>
      <div v-if="visible" class="modal-backdrop" @click="handleBackdropClick">
        <transition :name="`modal-${animation}`" appear>
          <div v-if="visible" class="modal-container" :class="[`modal-${size}`, { 'modal-fullscreen': fullscreen }]" @click.stop>
            <!-- 关闭按钮 -->
            <button v-if="showClose" class="modal-close" @click="handleClose">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </button>

            <!-- 头部 -->
            <div v-if="$slots.header || title" class="modal-header">
              <slot name="header">
                <h3 class="modal-title">{{ title }}</h3>
              </slot>
            </div>

            <!-- 内容 -->
            <div class="modal-body">
              <slot></slot>
            </div>

            <!-- 底部 -->
            <div v-if="$slots.footer" class="modal-footer">
              <slot name="footer"></slot>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </teleport>
</template>
  
  <script setup lang="ts" name="CustomModal">
import { watch } from 'vue';

interface Props {
  visible: boolean;
  title?: string;
  size?: 'small' | 'medium' | 'large';
  fullscreen?: boolean;
  showClose?: boolean;
  closeOnClickOutside?: boolean;
  closeOnEscape?: boolean;
  animation?: 'fade-scale' | 'slide-down' | 'slide-up' | 'slide-left' | 'slide-right' | 'rotate' | 'flip' | 'bounce';
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  size: 'medium',
  fullscreen: false,
  showClose: true,
  closeOnClickOutside: true,
  closeOnEscape: true,
  animation: 'fade-scale'
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'close': [];
  'open': [];
}>();

const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const handleBackdropClick = () => {
  if (props.closeOnClickOutside) {
    handleClose();
  }
};

// ESC键关闭和页面滚动控制
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      emit('open');
      // 禁止页面滚动
      document.body.style.overflow = 'hidden';
      // 添加高层级类名
      document.body.classList.add('modal-open');
      if (props.closeOnEscape) {
        document.addEventListener('keydown', handleEscapeKey);
      }
    } else {
      // 恢复页面滚动
      document.body.style.overflow = '';
      // 移除高层级类名
      document.body.classList.remove('modal-open');
      document.removeEventListener('keydown', handleEscapeKey);
    }
  }
);

const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    handleClose();
  }
};

// 导出组件名称，用于调试
defineOptions({
  name: 'CustomModal'
});
</script>
  
  <style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  backdrop-filter: blur(4px);
}

.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

// 尺寸
.modal-small {
  width: 400px;
  max-width: 90vw;
}

.modal-medium {
  width: 600px;
  max-width: 90vw;
}

.modal-large {
  // width: 800px;
  width: 80vw;
  max-width: 90vw;
}

.modal-fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  max-height: none !important;
  border-radius: 0 !important;
}

.modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.2s;
  z-index: 10;

  &:hover {
    background: rgba(0, 0, 0, 0.2);
    color: #333;
    transform: scale(1.1);
  }
}

.modal-header {
  padding: 14px 14px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 0;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
  padding-bottom: 16px;
}

.modal-body {
  padding: 12px;
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  padding: 0 12px 12px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: auto;
  padding-top: 12px;
}

// 背景动画
.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
  transition: all 0.3s ease;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
  opacity: 0;
}

// 淡入放大动画
.modal-fade-scale-enter-active,
.modal-fade-scale-leave-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-fade-scale-enter-from,
.modal-fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

// 从上滑入
.modal-slide-down-enter-active,
.modal-slide-down-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-slide-down-enter-from,
.modal-slide-down-leave-to {
  opacity: 0;
  transform: translateY(-100px);
}

// 从下滑入
.modal-slide-up-enter-active,
.modal-slide-up-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-slide-up-enter-from,
.modal-slide-up-leave-to {
  opacity: 0;
  transform: translateY(100px);
}

// 从左滑入
.modal-slide-left-enter-active,
.modal-slide-left-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-slide-left-enter-from,
.modal-slide-left-leave-to {
  opacity: 0;
  transform: translateX(-100px);
}

// 从右滑入
.modal-slide-right-enter-active,
.modal-slide-right-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-slide-right-enter-from,
.modal-slide-right-leave-to {
  opacity: 0;
  transform: translateX(100px);
}

// 旋转进入
.modal-rotate-enter-active,
.modal-rotate-leave-active {
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.modal-rotate-enter-from,
.modal-rotate-leave-to {
  opacity: 0;
  transform: rotate(-180deg) scale(0.5);
}

// 翻转进入
.modal-flip-enter-active,
.modal-flip-leave-active {
  transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.modal-flip-enter-from,
.modal-flip-leave-to {
  opacity: 0;
  transform: rotateY(-90deg) scale(0.8);
}

// 弹跳进入
.modal-bounce-enter-active {
  animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.modal-bounce-leave-active {
  animation: bounce-out 0.3s ease-in;
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce-out {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}
</style>
  
  <style lang="scss">
// 全局样式，确保弹窗层级最高
:global(.modal-open) {
  // 禁止页面滚动
  overflow: hidden !important;

  // 降低其他高层级元素的优先级
  .el-message,
  .el-notification,
  .el-loading-mask,
  .el-drawer,
  .el-popover,
  .el-tooltip,
  .vxe-modal--wrapper,
  .sidebar-container,
  .drawer-bg,
  .fixed-header {
    z-index: 9998 !important;
  }

  // 确保侧边栏等元素不会覆盖弹窗
  .app-wrapper,
  .layout-container {
    position: relative;
    z-index: 1;
  }

  // 阻止其他元素的点击事件（除了弹窗本身）
  * {
    pointer-events: none;
  }

  // 恢复弹窗内元素的点击事件
  .modal-backdrop,
  .modal-container,
  .modal-backdrop *,
  .modal-container * {
    pointer-events: auto;
  }
}

// 确保弹窗容器始终在最顶层
.modal-backdrop {
  z-index: 999999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}
</style>
  